// Photoshop脚本：快速导出画布尺寸
// 简化版本，专注核心功能

#target photoshop

// 检查文档
if (app.documents.length == 0) {
    alert("请先打开一个文档！");
} else {
    exportSelectedLayers();
}

function exportSelectedLayers() {
    var doc = app.activeDocument;
    
    // 获取选中的图层（使用最简单的方法）
    var selectedLayers = [];
    
    try {
        // 检查是否有多选
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            // 多选情况 - 使用分组方法
            selectedLayers = getMultiSelectedLayers();
        } else {
            // 单选情况 - 直接使用活动图层
            selectedLayers = [doc.activeLayer];
        }
    } catch (e) {
        // 出错时使用活动图层
        selectedLayers = [doc.activeLayer];
    }
    
    if (selectedLayers.length == 0) {
        alert("没有选中的图层！");
        return;
    }
    
    // 显示确认信息
    var layerList = "";
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = (selectedLayers[i].typename == "LayerSet") ? "[组] " : "[层] ";
        layerList += typeText + selectedLayers[i].name + "\n";
    }
    
    if (!confirm("导出 " + selectedLayers.length + " 个图层：\n\n" + layerList + "\n继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("选择导出位置");
    if (!folder) return;
    
    // 保存状态
    var originalHistory = doc.activeHistoryState;
    
    var success = 0;
    var errors = [];
    
    // 导出每个图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportSingleLayer(doc, selectedLayers[i], folder);
            success++;
        } catch (e) {
            errors.push(selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
    } catch (e) {}
    
    // 显示结果
    var result = "完成！成功：" + success + " 个";
    if (errors.length > 0) {
        result += "\n失败：" + errors.join("\n");
    }
    result += "\n位置：" + folder.fsName;
    alert(result);
}

// 获取多选图层（使用分组方法）
function getMultiSelectedLayers() {
    var selectedLayers = [];
    
    try {
        // 创建临时组
        var desc = new ActionDescriptor();
        var ref = new ActionReference();
        ref.putClass(stringIDToTypeID('layerSection'));
        desc.putReference(charIDToTypeID('null'), ref);
        
        var lref = new ActionReference();
        lref.putEnumerated(charIDToTypeID('Lyr '), charIDToTypeID('Ordn'), charIDToTypeID('Trgt'));
        desc.putReference(charIDToTypeID('From'), lref);
        
        executeAction(charIDToTypeID('Mk  '), desc, DialogModes.NO);
        
        // 获取组中的图层
        var group = app.activeDocument.activeLayer;
        if (group.typename == "LayerSet") {
            for (var i = 0; i < group.layers.length; i++) {
                selectedLayers.push(group.layers[i]);
            }
        } else {
            selectedLayers.push(group);
        }
        
        // 撤销分组
        executeAction(charIDToTypeID("undo"), undefined, DialogModes.NO);
        
    } catch (e) {
        // 如果分组方法失败，使用活动图层
        try {
            executeAction(charIDToTypeID("undo"), undefined, DialogModes.NO);
        } catch (ex) {}
        selectedLayers = [app.activeDocument.activeLayer];
    }
    
    return selectedLayers;
}

// 导出单个图层
function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档（与原画布相同尺寸）
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出PNG
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        // 清理
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

// 清理文件名
function cleanFileName(name) {
    var result = name;
    
    // 替换非法字符
    var illegalChars = ['\\', '/', ':', '*', '?', '"', '<', '>', '|'];
    for (var i = 0; i < illegalChars.length; i++) {
        while (result.indexOf(illegalChars[i]) >= 0) {
            result = result.replace(illegalChars[i], "_");
        }
    }
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
