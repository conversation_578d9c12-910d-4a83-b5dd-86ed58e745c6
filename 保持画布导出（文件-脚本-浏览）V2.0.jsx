// Photoshop脚本：按画布尺寸导出图层（支持图层组）
// 简化版本，支持图层组自动识别

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        exportSelectedItems();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function exportSelectedItems() {
    var doc = app.activeDocument;
    
    // 获取选中的项目（图层或图层组）
    var items = getSelectedItems();
    if (items.length == 0) {
        alert("请先选择要导出的图层或图层组！");
        return;
    }
    
    // 显示确认信息
    var itemList = "";
    for (var i = 0; i < items.length; i++) {
        var typeText = isLayerSet(items[i]) ? "[图层组] " : "[图层] ";
        itemList += "• " + typeText + items[i].name + "\n";
    }
    
    if (!confirm("准备导出 " + items.length + " 个项目：\n\n" + itemList + "\n是否继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("请选择导出位置");
    if (!folder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalLayer = doc.activeLayer;
    
    var success = 0;
    var failed = 0;
    var errors = [];
    
    // 导出每个项目
    for (var i = 0; i < items.length; i++) {
        try {
            exportItem(doc, items[i], folder);
            success++;
        } catch (e) {
            failed++;
            var typeText = isLayerSet(items[i]) ? "[图层组] " : "[图层] ";
            errors.push(typeText + items[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n";
    result += "成功：" + success + " 个\n";
    result += "位置：" + folder.fsName;
    
    if (failed > 0) {
        result += "\n\n失败：" + failed + " 个\n" + errors.join("\n");
    }
    
    alert(result);
}

function getSelectedItems() {
    var items = [];
    
    try {
        // 尝试获取多选项目
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            var list = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < list.count; i++) {
                var layerRef = list.getReference(i);
                var index = layerRef.getIndex();
                var item = getItemByIndex(index);
                if (item) items.push(item);
            }
        } else {
            items.push(app.activeDocument.activeLayer);
        }
    } catch (e) {
        items.push(app.activeDocument.activeLayer);
    }
    
    return items;
}

function getItemByIndex(index) {
    try {
        var ref = new ActionReference();
        ref.putIndex(charIDToTypeID("Lyr "), index);
        var desc = executeActionGet(ref);
        var name = desc.getString(charIDToTypeID("Nm  "));
        
        // 在文档中查找对应的项目
        var doc = app.activeDocument;
        
        // 先查找图层组
        for (var i = 0; i < doc.layerSets.length; i++) {
            if (doc.layerSets[i].name == name) {
                return doc.layerSets[i];
            }
        }
        
        // 再查找普通图层
        for (var i = 0; i < doc.artLayers.length; i++) {
            if (doc.artLayers[i].name == name) {
                return doc.artLayers[i];
            }
        }
        
        // 如果都没找到，尝试在所有图层中查找
        for (var i = 0; i < doc.layers.length; i++) {
            if (doc.layers[i].name == name) {
                return doc.layers[i];
            }
        }
    } catch (e) {}
    return null;
}

function isLayerSet(item) {
    try {
        return (item.typename == "LayerSet");
    } catch (e) {
        return false;
    }
}

function exportItem(doc, item, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制项目到临时文档
        app.activeDocument = doc;
        item.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = makeValidFileName(item.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

function makeValidFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "item";
    }
    
    return result;
}
