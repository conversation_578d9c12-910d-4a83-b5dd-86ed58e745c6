// Photoshop脚本：替代方案 - 完全不使用分组方法
// 使用更直接的方式获取选中图层

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        main();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function main() {
    var doc = app.activeDocument;
    
    // 使用替代方法获取选中图层
    var selectedLayers = getSelectedLayersAlternative();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层或图层组！");
        return;
    }
    
    // 显示确认信息
    var layerList = "";
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
        layerList += "• " + typeText + selectedLayers[i].name + "\n";
    }
    
    if (!confirm("准备导出 " + selectedLayers.length + " 个项目：\n\n" + layerList + "\n是否继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("请选择导出位置");
    if (!folder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalLayer = doc.activeLayer;
    
    var success = 0;
    var failed = 0;
    var errors = [];
    
    // 导出每个图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportSingleLayer(doc, selectedLayers[i], folder);
            success++;
        } catch (e) {
            failed++;
            var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
            errors.push(typeText + selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n";
    result += "成功：" + success + " 个\n";
    result += "位置：" + folder.fsName;
    
    if (failed > 0) {
        result += "\n\n失败：" + failed + " 个\n" + errors.join("\n");
    }
    
    alert(result);
}

// 辅助函数
function cTID(s) { return app.charIDToTypeID(s); }
function sTID(s) { return app.stringIDToTypeID(s); }

// 替代方法：使用Action Manager直接获取选中图层的索引
function getSelectedLayersAlternative() {
    var selectedLayers = [];
    
    try {
        // 方法1：尝试使用Action Manager获取选中图层的索引
        var selectedIndices = getSelectedLayerIndicesAM();
        
        if (selectedIndices.length > 0) {
            // 根据索引直接获取图层对象
            for (var i = 0; i < selectedIndices.length; i++) {
                var layer = getLayerByIndexAM(selectedIndices[i]);
                if (layer) {
                    selectedLayers.push(layer);
                }
            }
        } else {
            // 如果没有获取到，使用当前活动图层
            selectedLayers.push(app.activeDocument.activeLayer);
        }
        
    } catch (e) {
        // 备用方案：使用当前活动图层
        try {
            selectedLayers.push(app.activeDocument.activeLayer);
        } catch (ex) {}
    }
    
    return selectedLayers;
}

// 使用Action Manager获取选中图层的索引
function getSelectedLayerIndicesAM() {
    var indices = [];
    
    try {
        var ref = new ActionReference();
        ref.putEnumerated(cTID("Dcmn"), cTID("Ordn"), cTID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(sTID("targetLayers"))) {
            // 多选情况
            var list = desc.getList(sTID("targetLayers"));
            for (var i = 0; i < list.count; i++) {
                var layerRef = list.getReference(i);
                var index = layerRef.getIndex();
                indices.push(index);
            }
        } else {
            // 单选情况 - 获取当前活动图层的索引
            var activeRef = new ActionReference();
            activeRef.putEnumerated(cTID("Lyr "), cTID("Ordn"), cTID("Trgt"));
            var activeDesc = executeActionGet(activeRef);
            var activeIndex = activeDesc.getInteger(sTID("itemIndex"));
            indices.push(activeIndex);
        }
    } catch (e) {
        // 如果获取失败，返回空数组
    }
    
    return indices;
}

// 根据索引获取图层对象（使用Action Manager）
function getLayerByIndexAM(index) {
    try {
        var ref = new ActionReference();
        ref.putIndex(cTID("Lyr "), index);
        var desc = executeActionGet(ref);
        
        // 获取图层名称和ID
        var layerName = desc.getString(cTID("Nm  "));
        var layerID = desc.getInteger(sTID("layerID"));
        
        // 在文档中查找对应的图层
        return findLayerByNameAndID(app.activeDocument, layerName, layerID);
        
    } catch (e) {
        return null;
    }
}

// 根据名称和ID查找图层
function findLayerByNameAndID(doc, targetName, targetID) {
    // 递归查找函数
    function searchInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                
                // 首先匹配名称
                if (layer.name == targetName) {
                    // 如果可能的话，也验证ID
                    try {
                        var ref = new ActionReference();
                        ref.putIdentifier(cTID("Lyr "), targetID);
                        var desc = executeActionGet(ref);
                        var checkName = desc.getString(cTID("Nm  "));
                        if (checkName == targetName) {
                            return layer;
                        }
                    } catch (e) {
                        // 如果ID验证失败，仅凭名称匹配
                        return layer;
                    }
                }
                
                // 如果是图层组，递归查找
                if (layer.typename == "LayerSet") {
                    var found = searchInContainer(layer);
                    if (found) return found;
                }
            }
        }
        return null;
    }
    
    return searchInContainer(doc);
}

function isLayerGroup(layer) {
    try {
        return (layer.typename == "LayerSet");
    } catch (e) {
        return false;
    }
}

function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

function cleanFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
