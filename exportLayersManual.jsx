// Photoshop脚本：手动选择方案 - 让用户手动确认要导出的图层
// 完全避开自动检测的问题

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        main();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function main() {
    var doc = app.activeDocument;
    
    // 显示图层选择对话框
    var selectedLayers = showLayerSelectionDialog(doc);
    
    if (selectedLayers.length == 0) {
        alert("没有选择要导出的图层！");
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("请选择导出位置");
    if (!folder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalLayer = doc.activeLayer;
    
    var success = 0;
    var failed = 0;
    var errors = [];
    
    // 导出每个图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportSingleLayer(doc, selectedLayers[i], folder);
            success++;
        } catch (e) {
            failed++;
            var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
            errors.push(typeText + selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n";
    result += "成功：" + success + " 个\n";
    result += "位置：" + folder.fsName;
    
    if (failed > 0) {
        result += "\n\n失败：" + failed + " 个\n" + errors.join("\n");
    }
    
    alert(result);
}

// 显示图层选择对话框
function showLayerSelectionDialog(doc) {
    var selectedLayers = [];
    
    // 获取所有图层的列表
    var allLayers = getAllLayers(doc);
    
    if (allLayers.length == 0) {
        alert("文档中没有图层！");
        return selectedLayers;
    }
    
    // 创建选择列表
    var layerNames = [];
    for (var i = 0; i < allLayers.length; i++) {
        var typeText = isLayerGroup(allLayers[i]) ? "[图层组] " : "[图层] ";
        layerNames.push(typeText + allLayers[i].name);
    }
    
    // 显示当前活动图层
    var currentActiveLayer = doc.activeLayer;
    var currentIndex = -1;
    for (var i = 0; i < allLayers.length; i++) {
        if (allLayers[i] == currentActiveLayer) {
            currentIndex = i;
            break;
        }
    }
    
    var message = "请选择要导出的图层：\n\n";
    message += "当前活动图层：" + (currentIndex >= 0 ? layerNames[currentIndex] : "未知") + "\n\n";
    message += "选项：\n";
    message += "1. 只导出当前活动图层\n";
    message += "2. 导出所有可见图层\n";
    message += "3. 导出所有图层（包括隐藏的）\n";
    message += "4. 取消\n\n";
    message += "请输入选项编号（1-4）：";
    
    var choice = prompt(message, "1");
    
    if (!choice || choice == "4") {
        return selectedLayers;
    }
    
    switch (choice) {
        case "1":
            // 只导出当前活动图层
            selectedLayers.push(currentActiveLayer);
            break;
            
        case "2":
            // 导出所有可见图层
            for (var i = 0; i < allLayers.length; i++) {
                if (allLayers[i].visible) {
                    selectedLayers.push(allLayers[i]);
                }
            }
            break;
            
        case "3":
            // 导出所有图层
            selectedLayers = allLayers.slice(); // 复制数组
            break;
            
        default:
            alert("无效的选项，将导出当前活动图层");
            selectedLayers.push(currentActiveLayer);
            break;
    }
    
    return selectedLayers;
}

// 获取文档中的所有图层
function getAllLayers(doc) {
    var allLayers = [];
    
    function collectLayers(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                allLayers.push(layer);
                
                // 如果是图层组，递归收集子图层
                if (layer.typename == "LayerSet") {
                    collectLayers(layer);
                }
            }
        }
    }
    
    collectLayers(doc);
    return allLayers;
}

function isLayerGroup(layer) {
    try {
        return (layer.typename == "LayerSet");
    } catch (e) {
        return false;
    }
}

function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

function cleanFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
