// Photoshop脚本：按画布尺寸导出选中图层
// 功能：
// 1. 导出当前选中的1个或多个图层
// 2. 以选中的图层命名导出
// 3. 导出的图层是以当前画布为大小
// 4. 导出对象在当前画布中的位置不变

#target photoshop

// 检查是否有打开的文档
if (app.documents.length == 0) {
    alert("请先打开一个文档！");
} else {
    main();
}

function main() {
    try {
        var doc = app.activeDocument;
        var selectedLayers = getSelectedLayers();
        
        if (selectedLayers.length == 0) {
            alert("请先选择要导出的图层！");
            return;
        }
        
        // 选择导出文件夹
        var exportFolder = Folder.selectDialog("选择导出文件夹");
        if (!exportFolder) {
            return;
        }
        
        // 保存当前状态
        var originalActiveLayer = doc.activeLayer;
        var originalHistory = doc.activeHistoryState;
        
        // 遍历选中的图层进行导出
        for (var i = 0; i < selectedLayers.length; i++) {
            exportLayerAsCanvasSize(doc, selectedLayers[i], exportFolder);
        }
        
        // 恢复原始状态
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalActiveLayer;
        
        alert("导出完成！共导出 " + selectedLayers.length + " 个图层。");
        
    } catch (e) {
        alert("导出过程中发生错误：" + e.message);
    }
}

// 获取当前选中的图层
function getSelectedLayers() {
    var selectedLayers = [];
    var ref = new ActionReference();
    ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
    var desc = executeActionGet(ref);
    
    if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
        var targetLayers = desc.getList(stringIDToTypeID("targetLayers"));
        for (var i = 0; i < targetLayers.count; i++) {
            var layerRef = targetLayers.getReference(i);
            var layerIndex = layerRef.getIndex();
            selectedLayers.push(app.activeDocument.layers[layerIndex]);
        }
    } else {
        // 如果没有多选，则返回当前活动图层
        selectedLayers.push(app.activeDocument.activeLayer);
    }
    
    return selectedLayers;
}

// 导出单个图层为画布尺寸
function exportLayerAsCanvasSize(doc, layer, exportFolder) {
    try {
        // 创建临时文档，尺寸与原画布相同
        var tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp_export",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 合并所有图层（如果图层组的话）
        tempDoc.flatten();
        
        // 生成文件名（清理图层名称中的非法字符）
        var layerName = layer.name.replace(/[\\/:*?"<>|]/g, "_");
        var fileName = layerName + ".png";
        var exportFile = new File(exportFolder + "/" + fileName);
        
        // 导出为PNG
        var pngOptions = new PNGSaveOptions();
        pngOptions.compression = 6;
        pngOptions.interlaced = false;
        
        tempDoc.exportDocument(exportFile, ExportType.SAVEFORWEB, pngOptions);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        // 如果临时文档存在，确保关闭它
        try {
            if (tempDoc) {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            }
        } catch (closeError) {
            // 忽略关闭错误
        }
        
        app.activeDocument = doc;
        throw new Error("导出图层 '" + layer.name + "' 时发生错误：" + e.message);
    }
}

// 使用Web导出方式的替代方案
function exportLayerAsCanvasSizeWeb(doc, layer, exportFolder) {
    try {
        // 隐藏所有其他图层
        hideAllLayersExcept(doc, layer);
        
        // 生成文件名
        var layerName = layer.name.replace(/[\\/:*?"<>|]/g, "_");
        var fileName = layerName + ".png";
        var exportFile = new File(exportFolder + "/" + fileName);
        
        // 使用存储为Web所用格式导出
        var webOptions = new ExportOptionsSaveForWeb();
        webOptions.format = SaveDocumentType.PNG;
        webOptions.PNG8 = false; // 使用PNG-24
        webOptions.transparency = true;
        webOptions.interlaced = false;
        webOptions.includeProfile = false;
        
        doc.exportDocument(exportFile, ExportType.SAVEFORWEB, webOptions);
        
        // 恢复所有图层的可见性
        showAllLayers(doc);
        
    } catch (e) {
        // 确保恢复图层可见性
        showAllLayers(doc);
        throw new Error("导出图层 '" + layer.name + "' 时发生错误：" + e.message);
    }
}

// 隐藏除指定图层外的所有图层
function hideAllLayersExcept(doc, keepVisibleLayer) {
    for (var i = 0; i < doc.layers.length; i++) {
        if (doc.layers[i] != keepVisibleLayer) {
            doc.layers[i].visible = false;
        } else {
            doc.layers[i].visible = true;
        }
    }
}

// 显示所有图层
function showAllLayers(doc) {
    for (var i = 0; i < doc.layers.length; i++) {
        doc.layers[i].visible = true;
    }
}
