// Photoshop脚本：快速按画布尺寸导出选中图层
// 专为快速导出优化，避免使用"存储为"方式

#target photoshop

// 主函数
function main() {
    // 检查是否有打开的文档
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
        return;
    }
    
    var doc = app.activeDocument;
    var selectedLayers = getSelectedLayers();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层！");
        return;
    }
    
    // 选择导出文件夹
    var exportFolder = Folder.selectDialog("选择导出文件夹");
    if (!exportFolder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    
    try {
        // 批量导出
        for (var i = 0; i < selectedLayers.length; i++) {
            exportSingleLayer(doc, selectedLayers[i], exportFolder);
        }
        
        alert("成功导出 " + selectedLayers.length + " 个图层！");
        
    } catch (e) {
        alert("导出失败：" + e.message);
    } finally {
        // 恢复历史状态
        doc.activeHistoryState = originalHistory;
    }
}

// 获取选中的图层
function getSelectedLayers() {
    var selectedLayers = [];
    
    try {
        // 获取选中图层的引用
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            // 多个图层被选中
            var targetLayers = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < targetLayers.count; i++) {
                var layerRef = targetLayers.getReference(i);
                var layerIndex = layerRef.getIndex();
                selectedLayers.push(getLayerByIndex(layerIndex));
            }
        } else {
            // 只有一个图层被选中
            selectedLayers.push(app.activeDocument.activeLayer);
        }
    } catch (e) {
        // 如果获取失败，使用当前活动图层
        selectedLayers.push(app.activeDocument.activeLayer);
    }
    
    return selectedLayers;
}

// 根据索引获取图层对象
function getLayerByIndex(index) {
    var ref = new ActionReference();
    ref.putIndex(charIDToTypeID("Lyr "), index);
    var desc = executeActionGet(ref);
    var layerName = desc.getString(charIDToTypeID("Nm  "));
    
    // 在文档中查找对应名称的图层
    var doc = app.activeDocument;
    for (var i = 0; i < doc.layers.length; i++) {
        if (doc.layers[i].name == layerName) {
            return doc.layers[i];
        }
    }
    return null;
}

// 导出单个图层
function exportSingleLayer(doc, layer, exportFolder) {
    var tempDoc = null;
    
    try {
        // 方法：创建与画布同尺寸的临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档并导出
        app.activeDocument = tempDoc;
        
        // 清理文件名
        var cleanName = layer.name.replace(/[\\/:*?"<>|]/g, "_");
        var exportFile = new File(exportFolder + "/" + cleanName + ".png");
        
        // 使用快速PNG导出
        exportPNG(tempDoc, exportFile);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        // 清理临时文档
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        app.activeDocument = doc;
        throw e;
    }
}

// PNG导出函数
function exportPNG(doc, file) {
    // 使用exportDocument方法，这是最快的导出方式
    var options = new ExportOptionsSaveForWeb();
    options.format = SaveDocumentType.PNG;
    options.PNG8 = false; // 使用PNG-24保证质量
    options.transparency = true;
    options.interlaced = false;
    options.includeProfile = false;
    options.optimized = true;
    
    doc.exportDocument(file, ExportType.SAVEFORWEB, options);
}

// 执行主函数
main();
