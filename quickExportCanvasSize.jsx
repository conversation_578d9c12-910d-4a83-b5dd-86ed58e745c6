// Photoshop脚本：快速按画布尺寸导出选中图层
// 功能：
// 1. 导出当前选中的1个或多个图层
// 2. 以选中的图层命名导出
// 3. 导出的图层是以当前画布为大小
// 4. 导出对象在当前画布中的位置不变
// 5. 导入脚本后准备导出选择可以指定导出位置
// 6. 导出后弹出导出成功

#target photoshop

// 主函数
function main() {
    // 检查是否有打开的文档
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
        return;
    }

    var doc = app.activeDocument;
    var selectedLayers = getSelectedLayers();

    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层！\n\n提示：在图层面板中选择一个或多个图层");
        return;
    }

    // 显示选中的图层信息
    var layerNames = [];
    for (var i = 0; i < selectedLayers.length; i++) {
        layerNames.push(selectedLayers[i].name);
    }

    var confirmMessage = "准备导出以下图层：\n\n" + layerNames.join("\n") + "\n\n是否继续？";
    if (!confirm(confirmMessage)) {
        return;
    }

    // 选择导出文件夹
    var exportFolder = Folder.selectDialog("请选择导出位置");
    if (!exportFolder) {
        return;
    }

    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalActiveLayer = doc.activeLayer;

    var successCount = 0;
    var failedLayers = [];

    try {
        // 批量导出
        for (var i = 0; i < selectedLayers.length; i++) {
            try {
                exportSingleLayer(doc, selectedLayers[i], exportFolder);
                successCount++;
            } catch (e) {
                failedLayers.push(selectedLayers[i].name + ": " + e.message);
            }
        }

        // 显示导出结果
        var resultMessage = "导出完成！\n\n";
        resultMessage += "成功导出：" + successCount + " 个图层\n";
        resultMessage += "导出位置：" + exportFolder.fsName;

        if (failedLayers.length > 0) {
            resultMessage += "\n\n失败：" + failedLayers.length + " 个图层\n";
            resultMessage += failedLayers.join("\n");
        }

        alert(resultMessage);

    } catch (e) {
        alert("导出过程中发生错误：" + e.message);
    } finally {
        // 恢复历史状态
        try {
            doc.activeHistoryState = originalHistory;
            doc.activeLayer = originalActiveLayer;
        } catch (e) {
            // 忽略恢复错误
        }
    }
}

// 获取选中的图层
function getSelectedLayers() {
    var selectedLayers = [];
    
    try {
        // 获取选中图层的引用
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            // 多个图层被选中
            var targetLayers = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < targetLayers.count; i++) {
                var layerRef = targetLayers.getReference(i);
                var layerIndex = layerRef.getIndex();
                selectedLayers.push(getLayerByIndex(layerIndex));
            }
        } else {
            // 只有一个图层被选中
            selectedLayers.push(app.activeDocument.activeLayer);
        }
    } catch (e) {
        // 如果获取失败，使用当前活动图层
        selectedLayers.push(app.activeDocument.activeLayer);
    }
    
    return selectedLayers;
}

// 根据索引获取图层对象
function getLayerByIndex(index) {
    var ref = new ActionReference();
    ref.putIndex(charIDToTypeID("Lyr "), index);
    var desc = executeActionGet(ref);
    var layerName = desc.getString(charIDToTypeID("Nm  "));
    
    // 在文档中查找对应名称的图层
    var doc = app.activeDocument;
    for (var i = 0; i < doc.layers.length; i++) {
        if (doc.layers[i].name == layerName) {
            return doc.layers[i];
        }
    }
    return null;
}

// 导出单个图层
function exportSingleLayer(doc, layer, exportFolder) {
    var tempDoc = null;
    
    try {
        // 方法：创建与画布同尺寸的临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档并导出
        app.activeDocument = tempDoc;
        
        // 清理文件名中的非法字符
        var cleanName = cleanFileName(layer.name);
        var exportFile = new File(exportFolder + "/" + cleanName + ".png");
        
        // 使用快速PNG导出
        exportPNG(tempDoc, exportFile);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        // 清理临时文档
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        app.activeDocument = doc;
        throw e;
    }
}

// 清理文件名函数
function cleanFileName(name) {
    // 替换非法字符为下划线
    var cleanName = name;
    cleanName = cleanName.replace(/\\/g, "_");
    cleanName = cleanName.replace(/\//g, "_");
    cleanName = cleanName.replace(/:/g, "_");
    cleanName = cleanName.replace(/\*/g, "_");
    cleanName = cleanName.replace(/\?/g, "_");
    cleanName = cleanName.replace(/"/g, "_");
    cleanName = cleanName.replace(/</g, "_");
    cleanName = cleanName.replace(/>/g, "_");
    cleanName = cleanName.replace(/\|/g, "_");

    // 限制文件名长度
    if (cleanName.length > 100) {
        cleanName = cleanName.substring(0, 100);
    }

    // 确保文件名不为空
    if (cleanName == "" || cleanName == "_") {
        cleanName = "layer";
    }

    return cleanName;
}

// PNG导出函数
function exportPNG(doc, file) {
    // 使用exportDocument方法，这是最快的导出方式
    var options = new ExportOptionsSaveForWeb();
    options.format = SaveDocumentType.PNG;
    options.PNG8 = false; // 使用PNG-24保证质量
    options.transparency = true;
    options.interlaced = false;
    options.includeProfile = false;
    options.optimized = true;

    doc.exportDocument(file, ExportType.SAVEFORWEB, options);
}

// 执行主函数
main();
