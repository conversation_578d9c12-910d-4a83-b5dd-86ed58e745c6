// Photoshop脚本：完美的图层导出解决方案
// 使用正确的方法获取选中图层，解决所有选择识别问题
// 基于GitHub上的成熟解决方案

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        main();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function main() {
    var doc = app.activeDocument;
    
    // 使用正确的方法获取选中图层
    var selectedLayers = getSelectedLayersCorrect();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层或图层组！");
        return;
    }
    
    // 显示确认信息
    var layerList = "";
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
        layerList += "• " + typeText + selectedLayers[i].name + "\n";
    }
    
    if (!confirm("准备导出 " + selectedLayers.length + " 个项目：\n\n" + layerList + "\n是否继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("请选择导出位置");
    if (!folder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalLayer = doc.activeLayer;
    
    var success = 0;
    var failed = 0;
    var errors = [];
    
    // 导出每个图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportSingleLayer(doc, selectedLayers[i], folder);
            success++;
        } catch (e) {
            failed++;
            var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
            errors.push(typeText + selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n";
    result += "成功：" + success + " 个\n";
    result += "位置：" + folder.fsName;
    
    if (failed > 0) {
        result += "\n\n失败：" + failed + " 个\n" + errors.join("\n");
    }
    
    alert(result);
}

// 辅助函数
function cTID(s) { return app.charIDToTypeID(s); }
function sTID(s) { return app.stringIDToTypeID(s); }

// 改进的获取选中图层方法
// 修复最顶层图层选择问题
function getSelectedLayersCorrect() {
    var selectedLayers = [];

    try {
        // 首先检查是否只有一个图层被选中
        if (isSingleLayerSelected()) {
            // 如果只选中一个图层，直接返回当前活动图层
            selectedLayers = [app.activeDocument.activeLayer];
            return selectedLayers;
        }

        // 多选情况下使用分组方法
        var originalActiveLayer = app.activeDocument.activeLayer;

        // 创建临时图层组
        newGroupFromLayers();

        // 获取新创建的组
        var group = app.activeDocument.activeLayer;

        // 检查是否真的创建了组
        if (group.typename == "LayerSet") {
            var layers = group.layers;

            // 验证图层数量的合理性
            var totalLayers = getTotalLayerCount(app.activeDocument);

            // 如果组内图层数量等于文档总图层数，说明出现了错误
            if (layers.length >= totalLayers) {
                // 这种情况下，很可能是最顶层图层选择的bug
                selectedLayers = [originalActiveLayer];
            } else {
                // 正常情况，收集组中的所有图层
                for (var i = 0; i < layers.length; i++) {
                    selectedLayers.push(layers[i]);
                }
            }
        } else {
            // 如果没有创建组，说明只选中了一个图层
            selectedLayers = [group];
        }

        // 撤销分组操作，恢复原始状态
        undo();

        // 验证结果的合理性
        if (selectedLayers.length == 0) {
            selectedLayers = [originalActiveLayer];
        }

    } catch (e) {
        // 如果出错，尝试撤销操作
        try {
            undo();
        } catch (ex) {}

        // 备用方案：使用当前活动图层
        try {
            selectedLayers = [app.activeDocument.activeLayer];
        } catch (ex) {}
    }

    return selectedLayers;
}

// 检查是否只选中了一个图层
function isSingleLayerSelected() {
    try {
        var ref = new ActionReference();
        ref.putEnumerated(cTID("Dcmn"), cTID("Ordn"), cTID("Trgt"));
        var desc = executeActionGet(ref);

        // 如果没有targetLayers属性，说明只选中了一个图层
        return !desc.hasKey(sTID("targetLayers"));
    } catch (e) {
        return true; // 出错时假设只选中一个图层
    }
}

// 获取文档中的总图层数（递归计算）
function getTotalLayerCount(doc) {
    var count = 0;

    function countInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                count++;
                if (container.layers[i].typename == "LayerSet") {
                    countInContainer(container.layers[i]);
                }
            }
        }
    }

    countInContainer(doc);
    return count;
}

// 创建图层组（从选中的图层）
function newGroupFromLayers() {
    var desc = new ActionDescriptor();
    var ref = new ActionReference();
    ref.putClass(sTID('layerSection'));
    desc.putReference(cTID('null'), ref);
    
    var lref = new ActionReference();
    lref.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
    desc.putReference(cTID('From'), lref);
    
    executeAction(cTID('Mk  '), desc, DialogModes.NO);
}

// 撤销操作
function undo() {
    executeAction(cTID("undo"), undefined, DialogModes.NO);
}

function isLayerGroup(layer) {
    try {
        return (layer.typename == "LayerSet");
    } catch (e) {
        return false;
    }
}

function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

function cleanFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
