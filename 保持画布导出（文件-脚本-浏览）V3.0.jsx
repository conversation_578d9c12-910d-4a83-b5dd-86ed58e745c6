// Photoshop脚本：完美的图层导出解决方案
// 使用正确的方法获取选中图层，解决所有选择识别问题
// 基于GitHub上的成熟解决方案

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        main();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function main() {
    var doc = app.activeDocument;
    
    // 使用正确的方法获取选中图层
    var selectedLayers = getSelectedLayersCorrect();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层或图层组！");
        return;
    }
    
    // 显示确认信息
    var layerList = "";
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
        layerList += "• " + typeText + selectedLayers[i].name + "\n";
    }
    
    if (!confirm("准备导出 " + selectedLayers.length + " 个项目：\n\n" + layerList + "\n是否继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("请选择导出位置");
    if (!folder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalLayer = doc.activeLayer;
    
    var success = 0;
    var failed = 0;
    var errors = [];
    
    // 导出每个图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportSingleLayer(doc, selectedLayers[i], folder);
            success++;
        } catch (e) {
            failed++;
            var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
            errors.push(typeText + selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n";
    result += "成功：" + success + " 个\n";
    result += "位置：" + folder.fsName;
    
    if (failed > 0) {
        result += "\n\n失败：" + failed + " 个\n" + errors.join("\n");
    }
    
    alert(result);
}

// 辅助函数
function cTID(s) { return app.charIDToTypeID(s); }
function sTID(s) { return app.stringIDToTypeID(s); }

// 正确的获取选中图层方法
// 这是目前唯一可靠的方法，基于GitHub上的成熟解决方案
function getSelectedLayersCorrect() {
    var selectedLayers = [];
    
    try {
        // 保存当前历史状态
        var originalHistory = app.activeDocument.activeHistoryState;
        
        // 创建临时图层组（这是获取选中图层的唯一可靠方法）
        newGroupFromLayers();
        
        // 获取新创建的组
        var group = app.activeDocument.activeLayer;
        var layers = group.layers;
        
        // 收集组中的所有图层
        for (var i = 0; i < layers.length; i++) {
            selectedLayers.push(layers[i]);
        }
        
        // 撤销分组操作，恢复原始状态
        undo();
        
    } catch (e) {
        // 如果出错，尝试撤销操作
        try {
            undo();
        } catch (ex) {}
        
        // 备用方案：使用当前活动图层
        try {
            selectedLayers = [app.activeDocument.activeLayer];
        } catch (ex) {}
    }
    
    return selectedLayers;
}

// 创建图层组（从选中的图层）
function newGroupFromLayers() {
    var desc = new ActionDescriptor();
    var ref = new ActionReference();
    ref.putClass(sTID('layerSection'));
    desc.putReference(cTID('null'), ref);
    
    var lref = new ActionReference();
    lref.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
    desc.putReference(cTID('From'), lref);
    
    executeAction(cTID('Mk  '), desc, DialogModes.NO);
}

// 撤销操作
function undo() {
    executeAction(cTID("undo"), undefined, DialogModes.NO);
}

function isLayerGroup(layer) {
    try {
        return (layer.typename == "LayerSet");
    } catch (e) {
        return false;
    }
}

function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

function cleanFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
