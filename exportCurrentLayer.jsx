// Photoshop脚本：导出当前活动图层（最简单版本）
// 避免复杂的多选检测，直接导出当前活动图层

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        exportCurrentActiveLayer();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function exportCurrentActiveLayer() {
    var doc = app.activeDocument;
    
    try {
        var activeLayer = doc.activeLayer;
        var isGroup = (activeLayer.typename == "LayerSet");
        var typeText = isGroup ? "[图层组] " : "[图层] ";
        
        if (!confirm("准备导出当前活动图层：\n\n• " + typeText + activeLayer.name + "\n\n是否继续？")) {
            return;
        }
        
        // 选择导出位置
        var folder = Folder.selectDialog("请选择导出位置");
        if (!folder) return;
        
        // 保存当前状态
        var originalHistory = doc.activeHistoryState;
        
        try {
            exportSingleLayer(doc, activeLayer, folder);
            alert("导出成功！\n\n文件：" + activeLayer.name + ".png\n位置：" + folder.fsName);
        } catch (e) {
            alert("导出失败：" + e.message);
        } finally {
            // 恢复状态
            try {
                doc.activeHistoryState = originalHistory;
            } catch (e) {}
        }
        
    } catch (e) {
        alert("无法获取当前活动图层：" + e.message);
    }
}

function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

function cleanFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
