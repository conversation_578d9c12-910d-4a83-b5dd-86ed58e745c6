// Photoshop脚本：导出画布尺寸（避免分组bug）
// 完全不使用分组方法，避免最上层图层盖印所有图层的问题

#target photoshop

// 检查文档
if (app.documents.length == 0) {
    alert("请先打开一个文档！");
} else {
    exportSelectedLayers();
}

function exportSelectedLayers() {
    var doc = app.activeDocument;
    
    // 使用隐藏其他图层的方法来实现单图层导出
    var selectedLayers = getSelectedLayersWithoutGrouping();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层！");
        return;
    }
    
    // 显示确认信息
    var layerList = "";
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = (selectedLayers[i].typename == "LayerSet") ? "[图层组] " : "[图层] ";
        layerList += "• " + typeText + selectedLayers[i].name + "\n";
    }
    
    if (!confirm("准备导出 " + selectedLayers.length + " 个图层：\n\n" + layerList + "\n是否继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("选择导出位置");
    if (!folder) return;
    
    // 保存所有图层的可见性状态
    var layerStates = saveAllLayerStates(doc);
    var originalHistory = doc.activeHistoryState;
    var originalActiveLayer = doc.activeLayer;
    
    var success = 0;
    var errors = [];
    
    // 逐个导出图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportSingleLayerByHiding(doc, selectedLayers[i], folder, layerStates);
            success++;
        } catch (e) {
            errors.push(selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复所有图层状态
    restoreAllLayerStates(doc, layerStates);
    
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalActiveLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n成功：" + success + " 个\n位置：" + folder.fsName;
    if (errors.length > 0) {
        result += "\n\n失败：" + errors.length + " 个\n" + errors.join("\n");
    }
    alert(result);
}

// 不使用分组方法获取选中图层
function getSelectedLayersWithoutGrouping() {
    var selectedLayers = [];
    
    try {
        // 检查是否有多选
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            // 多选情况 - 使用索引方法
            var list = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < list.count; i++) {
                var layerRef = list.getReference(i);
                var layerIndex = layerRef.getIndex();
                var layer = getLayerByIndexSafe(layerIndex);
                if (layer) {
                    selectedLayers.push(layer);
                }
            }
        } else {
            // 单选情况
            selectedLayers.push(app.activeDocument.activeLayer);
        }
    } catch (e) {
        // 出错时使用活动图层
        selectedLayers.push(app.activeDocument.activeLayer);
    }
    
    return selectedLayers;
}

// 安全地根据索引获取图层
function getLayerByIndexSafe(index) {
    try {
        var ref = new ActionReference();
        ref.putIndex(charIDToTypeID("Lyr "), index);
        var desc = executeActionGet(ref);
        var layerName = desc.getString(charIDToTypeID("Nm  "));
        
        // 在文档中查找对应名称的图层
        return findLayerByName(app.activeDocument, layerName);
    } catch (e) {
        return null;
    }
}

// 查找图层
function findLayerByName(doc, targetName) {
    function searchInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                if (layer.name == targetName) {
                    return layer;
                }
                if (layer.typename == "LayerSet") {
                    var found = searchInContainer(layer);
                    if (found) return found;
                }
            }
        }
        return null;
    }
    return searchInContainer(doc);
}

// 保存所有图层状态
function saveAllLayerStates(doc) {
    var states = [];
    
    function saveLayerStates(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                states.push({
                    layer: layer,
                    visible: layer.visible
                });
                
                if (layer.typename == "LayerSet") {
                    saveLayerStates(layer);
                }
            }
        }
    }
    
    saveLayerStates(doc);
    return states;
}

// 恢复所有图层状态
function restoreAllLayerStates(doc, states) {
    for (var i = 0; i < states.length; i++) {
        try {
            states[i].layer.visible = states[i].visible;
        } catch (e) {
            // 忽略无法恢复的图层
        }
    }
}

// 通过隐藏其他图层的方法导出单个图层
function exportSingleLayerByHiding(doc, targetLayer, folder, layerStates) {
    try {
        // 隐藏所有图层
        hideAllLayers(doc);
        
        // 只显示目标图层
        showLayerAndParents(targetLayer);
        
        // 生成文件名
        var fileName = cleanFileName(targetLayer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出整个文档（此时只有目标图层可见）
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        doc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
    } catch (e) {
        throw new Error("导出失败: " + e.message);
    }
}

// 隐藏所有图层
function hideAllLayers(doc) {
    function hideInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                layer.visible = false;
                
                if (layer.typename == "LayerSet") {
                    hideInContainer(layer);
                }
            }
        }
    }
    
    hideInContainer(doc);
}

// 显示图层及其父级
function showLayerAndParents(layer) {
    try {
        layer.visible = true;
        
        // 如果图层在组内，需要显示父组
        var parent = layer.parent;
        while (parent && parent.typename == "LayerSet") {
            parent.visible = true;
            parent = parent.parent;
        }
        
        // 如果是图层组，显示组内所有图层
        if (layer.typename == "LayerSet") {
            showAllInGroup(layer);
        }
    } catch (e) {
        // 忽略错误
    }
}

// 显示组内所有图层
function showAllInGroup(group) {
    try {
        if (group.layers) {
            for (var i = 0; i < group.layers.length; i++) {
                var layer = group.layers[i];
                layer.visible = true;
                
                if (layer.typename == "LayerSet") {
                    showAllInGroup(layer);
                }
            }
        }
    } catch (e) {
        // 忽略错误
    }
}

// 清理文件名
function cleanFileName(name) {
    var result = name;
    
    // 替换非法字符
    var illegalChars = ['\\', '/', ':', '*', '?', '"', '<', '>', '|'];
    for (var i = 0; i < illegalChars.length; i++) {
        while (result.indexOf(illegalChars[i]) >= 0) {
            result = result.replace(illegalChars[i], "_");
        }
    }
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度和确保不为空
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
