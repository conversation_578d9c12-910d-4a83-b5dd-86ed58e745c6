// Photoshop脚本：导出画布尺寸（图层组合并版）
// 需求：
// 1. 导出当前选中的1个或多个图层
// 2. 以选中的图层命名导出
// 3. 导出的图层是以当前画布为大小
// 4. 导出对象在当前画布中的位置不变
// 5. 如果所选图层中有组的情况，那么这个组自动识别为一个导出图层（合并为单个图层）

#target photoshop

// 检查文档
if (app.documents.length == 0) {
    alert("请先打开一个文档！");
} else {
    main();
}

function main() {
    var doc = app.activeDocument;
    
    // 获取选中图层
    var selectedLayers = getSelectedLayers();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层！");
        return;
    }
    
    // 显示确认信息
    var layerList = "";
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = (selectedLayers[i].typename == "LayerSet") ? "[图层组-将合并] " : "[图层] ";
        layerList += "• " + typeText + selectedLayers[i].name + "\n";
    }
    
    if (!confirm("准备导出 " + selectedLayers.length + " 个项目：\n\n" + layerList + "\n注意：图层组将合并为单个图层导出\n\n是否继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("选择导出位置");
    if (!folder) return;
    
    // 保存状态
    var originalHistory = doc.activeHistoryState;
    var originalActiveLayer = doc.activeLayer;
    
    var success = 0;
    var errors = [];
    
    // 导出每个图层/图层组
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            if (selectedLayers[i].typename == "LayerSet") {
                // 图层组：合并导出
                exportLayerGroupMerged(doc, selectedLayers[i], folder);
            } else {
                // 普通图层：直接导出
                exportSingleLayer(doc, selectedLayers[i], folder);
            }
            success++;
        } catch (e) {
            errors.push(selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalActiveLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n成功：" + success + " 个\n位置：" + folder.fsName;
    if (errors.length > 0) {
        result += "\n\n失败：" + errors.length + " 个\n" + errors.join("\n");
    }
    alert(result);
}

// 获取选中图层
function getSelectedLayers() {
    var selectedLayers = [];
    
    try {
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            // 多选
            var list = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < list.count; i++) {
                var layerRef = list.getReference(i);
                var layerIndex = layerRef.getIndex();
                var layer = getLayerByIndex(layerIndex);
                if (layer) {
                    selectedLayers.push(layer);
                }
            }
        } else {
            // 单选
            selectedLayers.push(app.activeDocument.activeLayer);
        }
    } catch (e) {
        selectedLayers.push(app.activeDocument.activeLayer);
    }
    
    return selectedLayers;
}

// 根据索引获取图层
function getLayerByIndex(index) {
    try {
        var ref = new ActionReference();
        ref.putIndex(charIDToTypeID("Lyr "), index);
        var desc = executeActionGet(ref);
        var layerName = desc.getString(charIDToTypeID("Nm  "));
        
        return findLayerByName(app.activeDocument, layerName);
    } catch (e) {
        return null;
    }
}

// 查找图层
function findLayerByName(doc, targetName) {
    function searchInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                if (layer.name == targetName) {
                    return layer;
                }
                if (layer.typename == "LayerSet") {
                    var found = searchInContainer(layer);
                    if (found) return found;
                }
            }
        }
        return null;
    }
    
    return searchInContainer(doc);
}

// 导出图层组（合并为单个图层）
function exportLayerGroupMerged(doc, layerGroup, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp_group",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层组到临时文档
        app.activeDocument = doc;
        layerGroup.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 合并图层组为单个图层
        if (tempDoc.layers.length > 1) {
            // 如果有多个图层，进行合并
            tempDoc.flatten();
        }
        
        // 生成文件名
        var fileName = cleanFileName(layerGroup.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw new Error("导出图层组失败: " + e.message);
    }
}

// 导出单个图层
function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp_layer",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw new Error("导出图层失败: " + e.message);
    }
}

// 清理文件名
function cleanFileName(name) {
    var result = name;
    
    // 替换非法字符
    var illegalChars = ['\\', '/', ':', '*', '?', '"', '<', '>', '|'];
    for (var i = 0; i < illegalChars.length; i++) {
        while (result.indexOf(illegalChars[i]) >= 0) {
            result = result.replace(illegalChars[i], "_");
        }
    }
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
