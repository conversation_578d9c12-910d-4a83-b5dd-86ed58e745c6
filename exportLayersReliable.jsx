// Photoshop脚本：可靠的图层导出（使用Action Manager方法）
// 修复图层选择识别问题的最终版本

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        main();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function main() {
    var doc = app.activeDocument;
    
    // 获取选中的图层
    var selectedLayers = getSelectedLayersReliable();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层或图层组！");
        return;
    }
    
    // 显示确认信息
    var layerList = "";
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = selectedLayers[i].isGroup ? "[图层组] " : "[图层] ";
        layerList += "• " + typeText + selectedLayers[i].name + "\n";
    }
    
    if (!confirm("准备导出 " + selectedLayers.length + " 个项目：\n\n" + layerList + "\n是否继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("请选择导出位置");
    if (!folder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalLayer = doc.activeLayer;
    
    var success = 0;
    var failed = 0;
    var errors = [];
    
    // 导出每个图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportLayerByName(doc, selectedLayers[i].name, folder);
            success++;
        } catch (e) {
            failed++;
            var typeText = selectedLayers[i].isGroup ? "[图层组] " : "[图层] ";
            errors.push(typeText + selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n";
    result += "成功：" + success + " 个\n";
    result += "位置：" + folder.fsName;
    
    if (failed > 0) {
        result += "\n\n失败：" + failed + " 个\n" + errors.join("\n");
    }
    
    alert(result);
}

// 可靠的获取选中图层方法
function getSelectedLayersReliable() {
    var selectedLayers = [];
    
    try {
        // 使用Action Manager获取选中图层的详细信息
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            // 有多个图层被选中
            var list = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < list.count; i++) {
                var layerRef = list.getReference(i);
                var layerInfo = getLayerInfoByReference(layerRef);
                if (layerInfo) {
                    selectedLayers.push(layerInfo);
                }
            }
        } else {
            // 只有一个图层被选中
            var activeLayerInfo = getActiveLayerInfo();
            if (activeLayerInfo) {
                selectedLayers.push(activeLayerInfo);
            }
        }
    } catch (e) {
        // 备用方法：使用当前活动图层
        var activeLayerInfo = getActiveLayerInfo();
        if (activeLayerInfo) {
            selectedLayers.push(activeLayerInfo);
        }
    }
    
    return selectedLayers;
}

// 通过引用获取图层信息
function getLayerInfoByReference(layerRef) {
    try {
        var desc = executeActionGet(layerRef);
        var name = desc.getString(charIDToTypeID("Nm  "));
        var layerKind = desc.getEnumerationValue(charIDToTypeID("Lyrt"));
        var isGroup = (layerKind == charIDToTypeID("Grp "));
        
        return {
            name: name,
            isGroup: isGroup
        };
    } catch (e) {
        return null;
    }
}

// 获取当前活动图层信息
function getActiveLayerInfo() {
    try {
        var activeLayer = app.activeDocument.activeLayer;
        var isGroup = (activeLayer.typename == "LayerSet");
        
        return {
            name: activeLayer.name,
            isGroup: isGroup
        };
    } catch (e) {
        return null;
    }
}

// 通过名称导出图层
function exportLayerByName(doc, layerName, folder) {
    var tempDoc = null;
    
    try {
        // 查找并选择指定名称的图层
        var targetLayer = findLayerByName(doc, layerName);
        if (!targetLayer) {
            throw new Error("找不到图层: " + layerName);
        }
        
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        targetLayer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layerName) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

// 在文档中查找指定名称的图层
function findLayerByName(doc, targetName) {
    // 递归查找函数
    function searchInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                if (layer.name == targetName) {
                    return layer;
                }
                // 如果是图层组，递归查找
                if (layer.typename == "LayerSet") {
                    var found = searchInContainer(layer);
                    if (found) return found;
                }
            }
        }
        return null;
    }
    
    return searchInContainer(doc);
}

function cleanFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
