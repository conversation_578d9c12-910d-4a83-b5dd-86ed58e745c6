// Photoshop脚本：导出画布尺寸 - 最终版
// 彻底避免分组bug，使用最安全的方法

#target photoshop

// 主函数
function main() {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
        return;
    }
    
    var doc = app.activeDocument;
    
    // 获取选中图层（不使用分组）
    var selectedLayers = getSelectedLayersDirectly();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层！");
        return;
    }
    
    // 确认导出
    var layerNames = [];
    for (var i = 0; i < selectedLayers.length; i++) {
        var prefix = (selectedLayers[i].typename == "LayerSet") ? "[组] " : "[层] ";
        layerNames.push(prefix + selectedLayers[i].name);
    }
    
    if (!confirm("导出 " + selectedLayers.length + " 个图层：\n\n" + layerNames.join("\n") + "\n\n继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("选择导出位置");
    if (!folder) return;
    
    // 执行导出
    var results = exportLayers(doc, selectedLayers, folder);
    
    // 显示结果
    var message = "导出完成！\n成功：" + results.success + " 个\n位置：" + folder.fsName;
    if (results.errors.length > 0) {
        message += "\n\n失败：\n" + results.errors.join("\n");
    }
    alert(message);
}

// 直接获取选中图层（避免分组）
function getSelectedLayersDirectly() {
    var selectedLayers = [];
    
    try {
        // 检查多选
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            // 多选 - 直接通过索引获取
            var list = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < list.count; i++) {
                var layerRef = list.getReference(i);
                var index = layerRef.getIndex();
                var layer = getLayerByIndex(index);
                if (layer) selectedLayers.push(layer);
            }
        } else {
            // 单选
            selectedLayers.push(app.activeDocument.activeLayer);
        }
    } catch (e) {
        selectedLayers.push(app.activeDocument.activeLayer);
    }
    
    return selectedLayers;
}

// 根据索引获取图层
function getLayerByIndex(index) {
    try {
        var ref = new ActionReference();
        ref.putIndex(charIDToTypeID("Lyr "), index);
        var desc = executeActionGet(ref);
        var name = desc.getString(charIDToTypeID("Nm  "));
        return findLayerByName(name);
    } catch (e) {
        return null;
    }
}

// 查找图层
function findLayerByName(name) {
    var doc = app.activeDocument;
    
    function search(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                if (layer.name == name) return layer;
                if (layer.typename == "LayerSet") {
                    var found = search(layer);
                    if (found) return found;
                }
            }
        }
        return null;
    }
    
    return search(doc);
}

// 导出图层
function exportLayers(doc, layers, folder) {
    var success = 0;
    var errors = [];
    
    // 保存原始状态
    var originalHistory = doc.activeHistoryState;
    var originalActiveLayer = doc.activeLayer;
    var originalStates = saveVisibilityStates(doc);
    
    for (var i = 0; i < layers.length; i++) {
        try {
            exportSingleLayerSafely(doc, layers[i], folder);
            success++;
        } catch (e) {
            errors.push(layers[i].name + ": " + e.message);
        }
        
        // 每次导出后恢复状态
        restoreVisibilityStates(doc, originalStates);
    }
    
    // 最终恢复
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalActiveLayer;
    } catch (e) {}
    
    return { success: success, errors: errors };
}

// 安全导出单个图层
function exportSingleLayerSafely(doc, layer, folder) {
    // 隐藏所有图层
    setAllLayersVisibility(doc, false);
    
    // 只显示目标图层
    setLayerVisibility(layer, true);
    
    // 导出
    var fileName = cleanFileName(layer.name) + ".png";
    var file = new File(folder.fsName + "/" + fileName);
    
    var options = new ExportOptionsSaveForWeb();
    options.format = SaveDocumentType.PNG;
    options.PNG8 = false;
    options.transparency = true;
    options.interlaced = false;
    options.includeProfile = false;
    
    doc.exportDocument(file, ExportType.SAVEFORWEB, options);
}

// 设置所有图层可见性
function setAllLayersVisibility(doc, visible) {
    function setInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                layer.visible = visible;
                if (layer.typename == "LayerSet") {
                    setInContainer(layer);
                }
            }
        }
    }
    setInContainer(doc);
}

// 设置图层可见性（包括父级）
function setLayerVisibility(layer, visible) {
    layer.visible = visible;
    
    // 如果显示图层，需要显示父级
    if (visible) {
        var parent = layer.parent;
        while (parent && parent.typename == "LayerSet") {
            parent.visible = true;
            parent = parent.parent;
        }
    }
    
    // 如果是图层组，设置子图层
    if (layer.typename == "LayerSet") {
        setGroupVisibility(layer, visible);
    }
}

// 设置图层组可见性
function setGroupVisibility(group, visible) {
    if (group.layers) {
        for (var i = 0; i < group.layers.length; i++) {
            var layer = group.layers[i];
            layer.visible = visible;
            if (layer.typename == "LayerSet") {
                setGroupVisibility(layer, visible);
            }
        }
    }
}

// 保存可见性状态
function saveVisibilityStates(doc) {
    var states = [];
    
    function saveInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                states.push({ layer: layer, visible: layer.visible });
                if (layer.typename == "LayerSet") {
                    saveInContainer(layer);
                }
            }
        }
    }
    
    saveInContainer(doc);
    return states;
}

// 恢复可见性状态
function restoreVisibilityStates(doc, states) {
    for (var i = 0; i < states.length; i++) {
        try {
            states[i].layer.visible = states[i].visible;
        } catch (e) {}
    }
}

// 清理文件名
function cleanFileName(name) {
    var result = name;
    var illegal = ['\\', '/', ':', '*', '?', '"', '<', '>', '|'];
    
    for (var i = 0; i < illegal.length; i++) {
        while (result.indexOf(illegal[i]) >= 0) {
            result = result.replace(illegal[i], "_");
        }
    }
    
    // 去空格
    while (result.charAt(0) == " ") result = result.substring(1);
    while (result.charAt(result.length - 1) == " ") result = result.substring(0, result.length - 1);
    
    if (result.length > 100) result = result.substring(0, 100);
    if (result == "" || result == "_") result = "layer";
    
    return result;
}

// 执行
try {
    main();
} catch (e) {
    alert("脚本错误：" + e.message);
}
