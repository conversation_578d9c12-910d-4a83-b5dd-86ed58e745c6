// Photoshop脚本：按画布尺寸导出图层（支持图层组）
// 功能：
// 1. 导出当前选中的1个或多个图层
// 2. 以选中的图层命名导出
// 3. 导出的图层是以当前画布为大小
// 4. 导出对象在当前画布中的位置不变
// 5. 导入脚本后准备导出选择可以指定导出位置
// 6. 导出后弹出导出成功
// 7. 支持图层组：选中的图层组会被识别为一个导出单元

#target photoshop

// 检查是否有打开的文档
if (app.documents.length == 0) {
    alert("请先打开一个文档！");
} else {
    main();
}

function main() {
    try {
        var doc = app.activeDocument;
        var selectedItems = getSelectedItems(doc);
        
        if (selectedItems.length == 0) {
            alert("请先选择要导出的图层或图层组！\n\n提示：在图层面板中选择一个或多个图层/图层组");
            return;
        }
        
        // 显示选中的项目信息
        var itemNames = [];
        for (var i = 0; i < selectedItems.length; i++) {
            var typeText = selectedItems[i].isGroup ? "[组] " : "[图层] ";
            itemNames.push("• " + typeText + selectedItems[i].name);
        }
        
        var confirmMessage = "准备导出以下 " + selectedItems.length + " 个项目：\n\n" + itemNames.join("\n") + "\n\n是否继续？";
        if (!confirm(confirmMessage)) {
            return;
        }
        
        // 选择导出文件夹
        var exportFolder = Folder.selectDialog("请选择导出位置");
        if (!exportFolder) {
            return;
        }
        
        // 保存当前状态
        var originalActiveLayer = doc.activeLayer;
        var originalHistory = doc.activeHistoryState;
        var layerVisibilityStates = saveLayerVisibilityStates(doc);
        
        var successCount = 0;
        var failedItems = [];
        
        // 批量导出
        for (var i = 0; i < selectedItems.length; i++) {
            try {
                if (selectedItems[i].isGroup) {
                    exportLayerGroupAsCanvasSize(doc, selectedItems[i], exportFolder);
                } else {
                    exportLayerAsCanvasSize(doc, selectedItems[i], exportFolder);
                }
                successCount++;
            } catch (e) {
                var typeText = selectedItems[i].isGroup ? "[组] " : "[图层] ";
                failedItems.push("• " + typeText + selectedItems[i].name + ": " + e.message);
            }
        }
        
        // 恢复原始状态
        try {
            restoreLayerVisibilityStates(doc, layerVisibilityStates);
            doc.activeHistoryState = originalHistory;
            doc.activeLayer = originalActiveLayer;
        } catch (e) {
            // 忽略恢复错误
        }
        
        // 显示导出结果
        showExportResult(successCount, failedItems, exportFolder);
        
    } catch (e) {
        alert("导出过程中发生错误：\n" + e.message);
    }
}

// 获取选中的图层和图层组
function getSelectedItems(doc) {
    var selectedItems = [];
    
    try {
        // 尝试获取多选图层
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            var targetLayers = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < targetLayers.count; i++) {
                var layerRef = targetLayers.getReference(i);
                var layerIndex = layerRef.getIndex();
                var item = getLayerOrGroupByIndex(doc, layerIndex);
                if (item) {
                    selectedItems.push(item);
                }
            }
        } else {
            // 只有一个图层被选中
            var activeItem = getActiveLayerOrGroup(doc);
            if (activeItem) {
                selectedItems.push(activeItem);
            }
        }
    } catch (e) {
        // 如果获取失败，使用当前活动图层
        var activeItem = getActiveLayerOrGroup(doc);
        if (activeItem) {
            selectedItems.push(activeItem);
        }
    }
    
    return selectedItems;
}

// 根据索引获取图层或图层组
function getLayerOrGroupByIndex(doc, index) {
    try {
        var ref = new ActionReference();
        ref.putIndex(charIDToTypeID("Lyr "), index);
        var desc = executeActionGet(ref);
        var layerName = desc.getString(charIDToTypeID("Nm  "));
        var layerKind = desc.getEnumerationValue(charIDToTypeID("Lyrt"));
        
        // 判断是否为图层组
        var isGroup = (layerKind == charIDToTypeID("Grp "));
        
        // 在文档中查找对应名称的图层或图层组
        var item = findLayerOrGroupByName(doc, layerName);
        if (item) {
            return {
                layer: item,
                name: layerName,
                isGroup: isGroup
            };
        }
    } catch (e) {
        // 忽略错误
    }
    return null;
}

// 获取当前活动的图层或图层组
function getActiveLayerOrGroup(doc) {
    try {
        var activeLayer = doc.activeLayer;
        var isGroup = (activeLayer.typename == "LayerSet");
        
        return {
            layer: activeLayer,
            name: activeLayer.name,
            isGroup: isGroup
        };
    } catch (e) {
        return null;
    }
}

// 通过名称查找图层或图层组
function findLayerOrGroupByName(doc, name) {
    // 先在顶层查找
    for (var i = 0; i < doc.layers.length; i++) {
        if (doc.layers[i].name == name) {
            return doc.layers[i];
        }
    }
    
    // 递归查找图层组内的图层
    for (var i = 0; i < doc.layerSets.length; i++) {
        var found = findInLayerSet(doc.layerSets[i], name);
        if (found) return found;
    }
    
    return null;
}

// 在图层组中递归查找
function findInLayerSet(layerSet, name) {
    if (layerSet.name == name) {
        return layerSet;
    }
    
    // 查找图层组中的图层
    for (var i = 0; i < layerSet.artLayers.length; i++) {
        if (layerSet.artLayers[i].name == name) {
            return layerSet.artLayers[i];
        }
    }
    
    // 查找嵌套的图层组
    for (var i = 0; i < layerSet.layerSets.length; i++) {
        var found = findInLayerSet(layerSet.layerSets[i], name);
        if (found) return found;
    }
    
    return null;
}

// 导出图层组为画布尺寸
function exportLayerGroupAsCanvasSize(doc, groupItem, exportFolder) {
    var tempDoc = null;
    
    try {
        // 创建与原画布相同尺寸的临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp_export_group",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层组到临时文档
        app.activeDocument = doc;
        var duplicatedGroup = groupItem.layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var cleanName = cleanFileName(groupItem.name);
        var fileName = cleanName + ".png";
        var exportFile = new File(exportFolder.fsName + "/" + fileName);
        
        // 导出为PNG
        exportAsPNG(tempDoc, exportFile);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        // 清理临时文档
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {
                // 忽略关闭错误
            }
        }
        
        // 确保切换回原文档
        try {
            app.activeDocument = doc;
        } catch (ex) {
            // 忽略切换错误
        }
        
        throw new Error("导出图层组失败: " + e.message);
    }
}

// 导出单个图层为画布尺寸
function exportLayerAsCanvasSize(doc, layerItem, exportFolder) {
    var tempDoc = null;
    
    try {
        // 创建与原画布相同尺寸的临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp_export_layer",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        var duplicatedLayer = layerItem.layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var cleanName = cleanFileName(layerItem.name);
        var fileName = cleanName + ".png";
        var exportFile = new File(exportFolder.fsName + "/" + fileName);
        
        // 导出为PNG
        exportAsPNG(tempDoc, exportFile);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        // 清理临时文档
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {
                // 忽略关闭错误
            }
        }
        
        // 确保切换回原文档
        try {
            app.activeDocument = doc;
        } catch (ex) {
            // 忽略切换错误
        }
        
        throw new Error("导出图层失败: " + e.message);
    }
}

// 保存图层可见性状态
function saveLayerVisibilityStates(doc) {
    var states = [];
    for (var i = 0; i < doc.layers.length; i++) {
        states.push({
            layer: doc.layers[i],
            visible: doc.layers[i].visible
        });
    }
    return states;
}

// 恢复图层可见性状态
function restoreLayerVisibilityStates(doc, states) {
    for (var i = 0; i < states.length; i++) {
        try {
            states[i].layer.visible = states[i].visible;
        } catch (e) {
            // 忽略无法恢复的图层
        }
    }
}

// 清理文件名
function cleanFileName(name) {
    var cleanName = name;

    // 逐个替换非法字符
    cleanName = cleanName.replace(/\\/g, "_");
    cleanName = cleanName.replace(/\//g, "_");
    cleanName = cleanName.replace(/:/g, "_");
    cleanName = cleanName.replace(/\*/g, "_");
    cleanName = cleanName.replace(/\?/g, "_");
    cleanName = cleanName.replace(/"/g, "_");
    cleanName = cleanName.replace(/</g, "_");
    cleanName = cleanName.replace(/>/g, "_");
    cleanName = cleanName.replace(/\|/g, "_");

    // 移除首尾空格
    while (cleanName.charAt(0) == " ") {
        cleanName = cleanName.substring(1);
    }
    while (cleanName.charAt(cleanName.length - 1) == " ") {
        cleanName = cleanName.substring(0, cleanName.length - 1);
    }

    // 限制长度
    if (cleanName.length > 100) {
        cleanName = cleanName.substring(0, 100);
    }

    // 确保不为空
    if (cleanName == "" || cleanName == "_") {
        cleanName = "item_" + new Date().getTime();
    }

    return cleanName;
}

// 导出为PNG
function exportAsPNG(doc, file) {
    var options = new ExportOptionsSaveForWeb();
    options.format = SaveDocumentType.PNG;
    options.PNG8 = false; // 使用PNG-24
    options.transparency = true;
    options.interlaced = false;
    options.includeProfile = false;
    options.optimized = true;

    doc.exportDocument(file, ExportType.SAVEFORWEB, options);
}

// 显示导出结果
function showExportResult(successCount, failedItems, exportFolder) {
    var resultMessage = "=== 导出完成 ===\n\n";
    resultMessage += "✓ 成功导出：" + successCount + " 个项目\n";
    resultMessage += "📁 导出位置：" + exportFolder.fsName + "\n";

    if (failedItems.length > 0) {
        resultMessage += "\n❌ 失败：" + failedItems.length + " 个项目\n";
        resultMessage += failedItems.join("\n");
    }

    if (successCount > 0) {
        resultMessage += "\n🎉 导出成功！";
    }

    alert(resultMessage);
}
