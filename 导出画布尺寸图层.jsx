// Photoshop脚本：导出画布尺寸图层
// 需求：
// 1. 导出当前选中的1个或多个图层
// 2. 以选中的图层命名导出
// 3. 导出的图层是以当前画布为大小
// 4. 导出对象在当前画布中的位置不变
// 5. 如果所选图层中有组的情况，那么这个组自动识别为一个导出图层

#target photoshop

// 主函数
function main() {
    // 检查是否有打开的文档
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
        return;
    }
    
    var doc = app.activeDocument;
    
    // 获取选中的图层
    var selectedLayers = getSelectedLayers();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层！");
        return;
    }
    
    // 显示确认信息
    var layerNames = [];
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = (selectedLayers[i].typename == "LayerSet") ? "[图层组] " : "[图层] ";
        layerNames.push(typeText + selectedLayers[i].name);
    }
    
    if (!confirm("准备导出以下图层：\n\n" + layerNames.join("\n") + "\n\n是否继续？")) {
        return;
    }
    
    // 选择导出文件夹
    var exportFolder = Folder.selectDialog("选择导出文件夹");
    if (!exportFolder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalActiveLayer = doc.activeLayer;
    
    var successCount = 0;
    var failedLayers = [];
    
    // 导出每个图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportLayerAsCanvasSize(doc, selectedLayers[i], exportFolder);
            successCount++;
        } catch (e) {
            failedLayers.push(selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复原始状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalActiveLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n成功：" + successCount + " 个\n位置：" + exportFolder.fsName;
    if (failedLayers.length > 0) {
        result += "\n\n失败：" + failedLayers.length + " 个\n" + failedLayers.join("\n");
    }
    alert(result);
}

// 获取选中的图层（简化版本）
function getSelectedLayers() {
    var selectedLayers = [];
    
    try {
        // 尝试获取多选图层
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            // 多选情况
            var list = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < list.count; i++) {
                var layerRef = list.getReference(i);
                var layerIndex = layerRef.getIndex();
                var layer = getLayerByIndex(layerIndex);
                if (layer) {
                    selectedLayers.push(layer);
                }
            }
        } else {
            // 单选情况
            selectedLayers.push(app.activeDocument.activeLayer);
        }
    } catch (e) {
        // 如果获取失败，使用当前活动图层
        selectedLayers.push(app.activeDocument.activeLayer);
    }
    
    return selectedLayers;
}

// 根据索引获取图层
function getLayerByIndex(index) {
    try {
        var ref = new ActionReference();
        ref.putIndex(charIDToTypeID("Lyr "), index);
        var desc = executeActionGet(ref);
        var layerName = desc.getString(charIDToTypeID("Nm  "));
        
        // 在文档中查找对应名称的图层
        return findLayerByName(app.activeDocument, layerName);
    } catch (e) {
        return null;
    }
}

// 在文档中查找指定名称的图层
function findLayerByName(doc, targetName) {
    function searchInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                if (layer.name == targetName) {
                    return layer;
                }
                // 如果是图层组，递归查找
                if (layer.typename == "LayerSet") {
                    var found = searchInContainer(layer);
                    if (found) return found;
                }
            }
        }
        return null;
    }
    
    return searchInContainer(doc);
}

// 导出图层为画布尺寸
function exportLayerAsCanvasSize(doc, layer, exportFolder) {
    var tempDoc = null;
    
    try {
        // 创建与原画布相同尺寸的临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp_export",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var cleanName = cleanFileName(layer.name);
        var fileName = cleanName + ".png";
        var exportFile = new File(exportFolder.fsName + "/" + fileName);
        
        // 导出为PNG
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false; // 使用PNG-24
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(exportFile, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        // 清理临时文档
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        
        throw new Error("导出失败: " + e.message);
    }
}

// 清理文件名
function cleanFileName(name) {
    var result = name;
    
    // 替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}

// 执行主函数
try {
    main();
} catch (e) {
    alert("脚本执行错误：" + e.message);
}
