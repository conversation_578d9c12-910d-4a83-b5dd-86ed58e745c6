// Photoshop脚本：按画布尺寸导出图层（修复图层选择bug）
// 修复了图层选择识别问题

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        exportSelectedItems();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function exportSelectedItems() {
    var doc = app.activeDocument;
    
    // 获取选中的项目（使用更可靠的方法）
    var items = getSelectedItemsReliable();
    if (items.length == 0) {
        alert("请先选择要导出的图层或图层组！");
        return;
    }
    
    // 显示确认信息
    var itemList = "";
    for (var i = 0; i < items.length; i++) {
        var typeText = isLayerSet(items[i]) ? "[图层组] " : "[图层] ";
        itemList += "• " + typeText + items[i].name + "\n";
    }
    
    if (!confirm("准备导出 " + items.length + " 个项目：\n\n" + itemList + "\n是否继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("请选择导出位置");
    if (!folder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalLayer = doc.activeLayer;
    
    var success = 0;
    var failed = 0;
    var errors = [];
    
    // 导出每个项目
    for (var i = 0; i < items.length; i++) {
        try {
            exportItem(doc, items[i], folder);
            success++;
        } catch (e) {
            failed++;
            var typeText = isLayerSet(items[i]) ? "[图层组] " : "[图层] ";
            errors.push(typeText + items[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n";
    result += "成功：" + success + " 个\n";
    result += "位置：" + folder.fsName;
    
    if (failed > 0) {
        result += "\n\n失败：" + failed + " 个\n" + errors.join("\n");
    }
    
    alert(result);
}

// 更可靠的获取选中项目方法
function getSelectedItemsReliable() {
    var selectedItems = [];
    
    try {
        // 方法1：使用Action Manager获取选中的图层ID
        var selectedLayerIDs = getSelectedLayerIDs();
        
        if (selectedLayerIDs.length > 0) {
            // 根据ID查找对应的图层对象
            for (var i = 0; i < selectedLayerIDs.length; i++) {
                var layer = getLayerByID(selectedLayerIDs[i]);
                if (layer) {
                    selectedItems.push(layer);
                }
            }
        } else {
            // 如果没有获取到多选，使用当前活动图层
            selectedItems.push(app.activeDocument.activeLayer);
        }
    } catch (e) {
        // 备用方法：使用当前活动图层
        try {
            selectedItems.push(app.activeDocument.activeLayer);
        } catch (ex) {
            // 如果连活动图层都获取不到，返回空数组
        }
    }
    
    return selectedItems;
}

// 获取选中图层的ID列表
function getSelectedLayerIDs() {
    var selectedIDs = [];
    
    try {
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            var list = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < list.count; i++) {
                try {
                    var layerRef = list.getReference(i);
                    var layerID = layerRef.getIdentifier();
                    selectedIDs.push(layerID);
                } catch (e) {
                    // 如果无法获取ID，尝试获取索引
                    try {
                        var layerIndex = layerRef.getIndex();
                        var id = getLayerIDByIndex(layerIndex);
                        if (id) selectedIDs.push(id);
                    } catch (ex) {
                        // 忽略无法获取的图层
                    }
                }
            }
        }
    } catch (e) {
        // 如果获取失败，返回空数组
    }
    
    return selectedIDs;
}

// 根据索引获取图层ID
function getLayerIDByIndex(index) {
    try {
        var ref = new ActionReference();
        ref.putIndex(charIDToTypeID("Lyr "), index);
        var desc = executeActionGet(ref);
        return desc.getInteger(stringIDToTypeID("layerID"));
    } catch (e) {
        return null;
    }
}

// 根据ID获取图层对象
function getLayerByID(layerID) {
    try {
        var doc = app.activeDocument;
        
        // 递归搜索所有图层
        return findLayerByIDRecursive(doc, layerID);
    } catch (e) {
        return null;
    }
}

// 递归查找图层
function findLayerByIDRecursive(container, targetID) {
    // 检查容器中的所有图层
    if (container.layers) {
        for (var i = 0; i < container.layers.length; i++) {
            var layer = container.layers[i];
            
            // 获取图层ID并比较
            try {
                var ref = new ActionReference();
                ref.putIdentifier(charIDToTypeID("Lyr "), targetID);
                var desc = executeActionGet(ref);
                var layerName = desc.getString(charIDToTypeID("Nm  "));
                
                if (layer.name == layerName) {
                    return layer;
                }
            } catch (e) {
                // 继续查找
            }
            
            // 如果是图层组，递归查找
            if (layer.typename == "LayerSet") {
                var found = findLayerByIDRecursive(layer, targetID);
                if (found) return found;
            }
        }
    }
    
    return null;
}

function isLayerSet(item) {
    try {
        return (item.typename == "LayerSet");
    } catch (e) {
        return false;
    }
}

function exportItem(doc, item, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制项目到临时文档
        app.activeDocument = doc;
        item.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = makeValidFileName(item.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

function makeValidFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "item";
    }
    
    return result;
}
