// Photoshop脚本：使用Action方法检测选中图层
// 尝试不同的API来获取选中图层

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        main();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function main() {
    var doc = app.activeDocument;
    
    // 尝试多种方法获取选中图层
    var selectedLayers = getSelectedLayersMultiMethod();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层或图层组！");
        return;
    }
    
    // 显示确认信息
    var layerList = "";
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
        layerList += "• " + typeText + selectedLayers[i].name + "\n";
    }
    
    if (!confirm("准备导出 " + selectedLayers.length + " 个项目：\n\n" + layerList + "\n是否继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("请选择导出位置");
    if (!folder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalLayer = doc.activeLayer;
    
    var success = 0;
    var failed = 0;
    var errors = [];
    
    // 导出每个图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportSingleLayer(doc, selectedLayers[i], folder);
            success++;
        } catch (e) {
            failed++;
            var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
            errors.push(typeText + selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n";
    result += "成功：" + success + " 个\n";
    result += "位置：" + folder.fsName;
    
    if (failed > 0) {
        result += "\n\n失败：" + failed + " 个\n" + errors.join("\n");
    }
    
    alert(result);
}

// 使用多种方法尝试获取选中图层
function getSelectedLayersMultiMethod() {
    var selectedLayers = [];
    
    // 方法1：尝试使用hasMultipleTargets
    try {
        if (hasMultipleTargets()) {
            selectedLayers = getMultipleSelectedLayers();
        } else {
            selectedLayers = [app.activeDocument.activeLayer];
        }
    } catch (e) {
        // 方法1失败，尝试方法2
    }
    
    // 如果方法1没有结果，尝试方法2
    if (selectedLayers.length == 0) {
        try {
            selectedLayers = getSelectedLayersAM();
        } catch (e) {
            // 方法2失败，使用方法3
        }
    }
    
    // 如果前面的方法都失败，使用当前活动图层
    if (selectedLayers.length == 0) {
        try {
            selectedLayers = [app.activeDocument.activeLayer];
        } catch (e) {
            // 最后的备用方法也失败了
        }
    }
    
    return selectedLayers;
}

// 检查是否有多个目标被选中
function hasMultipleTargets() {
    try {
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        return desc.hasKey(stringIDToTypeID("targetLayers"));
    } catch (e) {
        return false;
    }
}

// 获取多个选中的图层
function getMultipleSelectedLayers() {
    var layers = [];
    
    try {
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        var list = desc.getList(stringIDToTypeID("targetLayers"));
        
        for (var i = 0; i < list.count; i++) {
            var layerRef = list.getReference(i);
            var layer = getLayerFromReference(layerRef);
            if (layer) {
                layers.push(layer);
            }
        }
    } catch (e) {
        // 获取失败
    }
    
    return layers;
}

// 从引用获取图层对象
function getLayerFromReference(layerRef) {
    try {
        var desc = executeActionGet(layerRef);
        var layerName = desc.getString(charIDToTypeID("Nm  "));
        
        // 在文档中查找对应的图层
        return findLayerByName(app.activeDocument, layerName);
    } catch (e) {
        return null;
    }
}

// 使用Action Manager方法获取选中图层
function getSelectedLayersAM() {
    var layers = [];
    
    try {
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            var list = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < list.count; i++) {
                var layerRef = list.getReference(i);
                var index = layerRef.getIndex();
                var layer = getLayerByIndex(index);
                if (layer) {
                    layers.push(layer);
                }
            }
        }
    } catch (e) {
        // AM方法失败
    }
    
    return layers;
}

// 通过索引获取图层
function getLayerByIndex(index) {
    try {
        var ref = new ActionReference();
        ref.putIndex(charIDToTypeID("Lyr "), index);
        var desc = executeActionGet(ref);
        var layerName = desc.getString(charIDToTypeID("Nm  "));
        
        return findLayerByName(app.activeDocument, layerName);
    } catch (e) {
        return null;
    }
}

// 在文档中查找图层
function findLayerByName(doc, targetName) {
    // 递归查找函数
    function searchInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                var layer = container.layers[i];
                if (layer.name == targetName) {
                    return layer;
                }
                if (layer.typename == "LayerSet") {
                    var found = searchInContainer(layer);
                    if (found) return found;
                }
            }
        }
        return null;
    }
    
    return searchInContainer(doc);
}

function isLayerGroup(layer) {
    try {
        return (layer.typename == "LayerSet");
    } catch (e) {
        return false;
    }
}

function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

function cleanFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
