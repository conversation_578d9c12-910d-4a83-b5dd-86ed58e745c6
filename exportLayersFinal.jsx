// Photoshop脚本：最终修复版本
// 使用最简单可靠的方法获取选中图层

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        main();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function main() {
    var doc = app.activeDocument;
    
    // 使用最简单的方法获取选中图层
    var selectedLayers = getSelectedLayersBasic();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层或图层组！");
        return;
    }
    
    // 显示确认信息
    var layerList = "";
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
        layerList += "• " + typeText + selectedLayers[i].name + "\n";
    }
    
    if (!confirm("准备导出 " + selectedLayers.length + " 个项目：\n\n" + layerList + "\n是否继续？")) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("请选择导出位置");
    if (!folder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalLayer = doc.activeLayer;
    
    var success = 0;
    var failed = 0;
    var errors = [];
    
    // 导出每个图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportSingleLayer(doc, selectedLayers[i], folder);
            success++;
        } catch (e) {
            failed++;
            var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
            errors.push(typeText + selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n";
    result += "成功：" + success + " 个\n";
    result += "位置：" + folder.fsName;
    
    if (failed > 0) {
        result += "\n\n失败：" + failed + " 个\n" + errors.join("\n");
    }
    
    alert(result);
}

// 最基础的获取选中图层方法
function getSelectedLayersBasic() {
    var selectedLayers = [];
    
    try {
        // 方法1：尝试使用简单的AM方法
        var layerIndices = [];
        
        try {
            var ref = new ActionReference();
            ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
            var desc = executeActionGet(ref);
            
            if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
                var list = desc.getList(stringIDToTypeID("targetLayers"));
                for (var i = 0; i < list.count; i++) {
                    var layerRef = list.getReference(i);
                    var index = layerRef.getIndex();
                    layerIndices.push(index);
                }
            }
        } catch (e) {
            // 如果AM方法失败，使用备用方法
        }
        
        // 如果获取到了索引，转换为图层对象
        if (layerIndices.length > 0) {
            for (var i = 0; i < layerIndices.length; i++) {
                var layer = getLayerByIndexSafe(layerIndices[i]);
                if (layer) {
                    selectedLayers.push(layer);
                }
            }
        } else {
            // 备用方法：使用当前活动图层
            selectedLayers.push(app.activeDocument.activeLayer);
        }
        
    } catch (e) {
        // 最后的备用方法：使用当前活动图层
        try {
            selectedLayers.push(app.activeDocument.activeLayer);
        } catch (ex) {
            // 如果连活动图层都获取不到，返回空数组
        }
    }
    
    return selectedLayers;
}

// 安全地通过索引获取图层
function getLayerByIndexSafe(index) {
    try {
        // 获取图层名称
        var ref = new ActionReference();
        ref.putIndex(charIDToTypeID("Lyr "), index);
        var desc = executeActionGet(ref);
        var layerName = desc.getString(charIDToTypeID("Nm  "));
        
        // 在文档中查找同名图层
        return findLayerByNameSimple(app.activeDocument, layerName);
        
    } catch (e) {
        return null;
    }
}

// 简单的图层查找方法
function findLayerByNameSimple(doc, targetName) {
    try {
        // 先在顶层图层中查找
        for (var i = 0; i < doc.layers.length; i++) {
            if (doc.layers[i].name == targetName) {
                return doc.layers[i];
            }
        }
        
        // 如果没找到，在图层组中查找
        for (var i = 0; i < doc.layerSets.length; i++) {
            var found = searchInLayerSet(doc.layerSets[i], targetName);
            if (found) return found;
        }
        
        // 如果还没找到，在艺术图层中查找
        for (var i = 0; i < doc.artLayers.length; i++) {
            if (doc.artLayers[i].name == targetName) {
                return doc.artLayers[i];
            }
        }
        
    } catch (e) {
        // 忽略错误
    }
    
    return null;
}

// 在图层组中搜索
function searchInLayerSet(layerSet, targetName) {
    try {
        // 检查图层组本身
        if (layerSet.name == targetName) {
            return layerSet;
        }
        
        // 检查图层组中的艺术图层
        for (var i = 0; i < layerSet.artLayers.length; i++) {
            if (layerSet.artLayers[i].name == targetName) {
                return layerSet.artLayers[i];
            }
        }
        
        // 检查嵌套的图层组
        for (var i = 0; i < layerSet.layerSets.length; i++) {
            var found = searchInLayerSet(layerSet.layerSets[i], targetName);
            if (found) return found;
        }
        
    } catch (e) {
        // 忽略错误
    }
    
    return null;
}

function isLayerGroup(layer) {
    try {
        return (layer.typename == "LayerSet");
    } catch (e) {
        return false;
    }
}

function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 导出
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

function cleanFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
