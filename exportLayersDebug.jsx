// Photoshop脚本：调试版本 - 专门解决最顶层图层问题
// 包含详细的调试信息，帮助分析问题

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        main();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function main() {
    var doc = app.activeDocument;
    
    // 显示调试信息
    var debugInfo = getDebugInfo();
    
    // 使用改进的方法获取选中图层
    var selectedLayers = getSelectedLayersImproved();
    
    if (selectedLayers.length == 0) {
        alert("请先选择要导出的图层或图层组！");
        return;
    }
    
    // 显示详细的确认信息（包含调试信息）
    var layerList = "";
    for (var i = 0; i < selectedLayers.length; i++) {
        var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
        layerList += "• " + typeText + selectedLayers[i].name + "\n";
    }
    
    var confirmMessage = "=== 调试信息 ===\n" + debugInfo + "\n\n";
    confirmMessage += "=== 检测到的选中图层 ===\n";
    confirmMessage += "数量：" + selectedLayers.length + " 个\n\n" + layerList + "\n是否继续导出？";
    
    if (!confirm(confirmMessage)) {
        return;
    }
    
    // 选择导出位置
    var folder = Folder.selectDialog("请选择导出位置");
    if (!folder) return;
    
    // 保存当前状态
    var originalHistory = doc.activeHistoryState;
    var originalLayer = doc.activeLayer;
    
    var success = 0;
    var failed = 0;
    var errors = [];
    
    // 导出每个图层
    for (var i = 0; i < selectedLayers.length; i++) {
        try {
            exportSingleLayer(doc, selectedLayers[i], folder);
            success++;
        } catch (e) {
            failed++;
            var typeText = isLayerGroup(selectedLayers[i]) ? "[图层组] " : "[图层] ";
            errors.push(typeText + selectedLayers[i].name + ": " + e.message);
        }
    }
    
    // 恢复状态
    try {
        doc.activeHistoryState = originalHistory;
        doc.activeLayer = originalLayer;
    } catch (e) {}
    
    // 显示结果
    var result = "导出完成！\n\n";
    result += "成功：" + success + " 个\n";
    result += "位置：" + folder.fsName;
    
    if (failed > 0) {
        result += "\n\n失败：" + failed + " 个\n" + errors.join("\n");
    }
    
    alert(result);
}

// 获取调试信息
function getDebugInfo() {
    var doc = app.activeDocument;
    var info = "";
    
    try {
        info += "文档总图层数：" + getTotalLayerCount(doc) + "\n";
        info += "当前活动图层：" + doc.activeLayer.name + "\n";
        info += "活动图层类型：" + doc.activeLayer.typename + "\n";
        info += "是否单选：" + (isSingleLayerSelected() ? "是" : "否") + "\n";
        
        // 尝试获取选中图层数量
        try {
            var ref = new ActionReference();
            ref.putEnumerated(cTID("Dcmn"), cTID("Ordn"), cTID("Trgt"));
            var desc = executeActionGet(ref);
            
            if (desc.hasKey(sTID("targetLayers"))) {
                var list = desc.getList(sTID("targetLayers"));
                info += "API检测到的选中数：" + list.count + "\n";
            } else {
                info += "API检测到的选中数：1（单选）\n";
            }
        } catch (e) {
            info += "API检测失败：" + e.message + "\n";
        }
        
    } catch (e) {
        info += "调试信息获取失败：" + e.message + "\n";
    }
    
    return info;
}

// 辅助函数
function cTID(s) { return app.charIDToTypeID(s); }
function sTID(s) { return app.stringIDToTypeID(s); }

// 改进的获取选中图层方法
function getSelectedLayersImproved() {
    var selectedLayers = [];
    
    try {
        // 首先检查是否只有一个图层被选中
        if (isSingleLayerSelected()) {
            selectedLayers = [app.activeDocument.activeLayer];
            return selectedLayers;
        }
        
        // 多选情况下使用分组方法，但增加验证
        var originalActiveLayer = app.activeDocument.activeLayer;
        var totalLayers = getTotalLayerCount(app.activeDocument);
        
        // 创建临时图层组
        newGroupFromLayers();
        
        // 获取新创建的组
        var group = app.activeDocument.activeLayer;
        
        // 检查是否真的创建了组
        if (group.typename == "LayerSet") {
            var layers = group.layers;
            
            // 关键验证：如果组内图层数量异常多，说明出现了bug
            if (layers.length >= totalLayers * 0.8) {
                // 这种情况下，很可能是最顶层图层选择的bug
                selectedLayers = [originalActiveLayer];
            } else {
                // 正常情况，收集组中的所有图层
                for (var i = 0; i < layers.length; i++) {
                    selectedLayers.push(layers[i]);
                }
            }
        } else {
            // 如果没有创建组，说明只选中了一个图层
            selectedLayers = [group];
        }
        
        // 撤销分组操作
        undo();
        
        // 最终验证
        if (selectedLayers.length == 0) {
            selectedLayers = [originalActiveLayer];
        }
        
    } catch (e) {
        // 如果出错，尝试撤销操作
        try {
            undo();
        } catch (ex) {}
        
        // 备用方案
        try {
            selectedLayers = [app.activeDocument.activeLayer];
        } catch (ex) {}
    }
    
    return selectedLayers;
}

// 检查是否只选中了一个图层
function isSingleLayerSelected() {
    try {
        var ref = new ActionReference();
        ref.putEnumerated(cTID("Dcmn"), cTID("Ordn"), cTID("Trgt"));
        var desc = executeActionGet(ref);
        
        return !desc.hasKey(sTID("targetLayers"));
    } catch (e) {
        return true;
    }
}

// 获取文档中的总图层数
function getTotalLayerCount(doc) {
    var count = 0;
    
    function countInContainer(container) {
        if (container.layers) {
            for (var i = 0; i < container.layers.length; i++) {
                count++;
                if (container.layers[i].typename == "LayerSet") {
                    countInContainer(container.layers[i]);
                }
            }
        }
    }
    
    countInContainer(doc);
    return count;
}

// 创建图层组
function newGroupFromLayers() {
    var desc = new ActionDescriptor();
    var ref = new ActionReference();
    ref.putClass(sTID('layerSection'));
    desc.putReference(cTID('null'), ref);
    
    var lref = new ActionReference();
    lref.putEnumerated(cTID('Lyr '), cTID('Ordn'), cTID('Trgt'));
    desc.putReference(cTID('From'), lref);
    
    executeAction(cTID('Mk  '), desc, DialogModes.NO);
}

// 撤销操作
function undo() {
    executeAction(cTID("undo"), undefined, DialogModes.NO);
}

function isLayerGroup(layer) {
    try {
        return (layer.typename == "LayerSet");
    } catch (e) {
        return false;
    }
}

function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        app.activeDocument = doc;
        layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        app.activeDocument = tempDoc;
        
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false;
        options.transparency = true;
        options.interlaced = false;
        options.includeProfile = false;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        app.activeDocument = doc;
        
    } catch (e) {
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        throw e;
    }
}

function cleanFileName(name) {
    var result = name;
    
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    if (result == "" || result == "_") {
        result = "layer";
    }
    
    return result;
}
