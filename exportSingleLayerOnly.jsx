// Photoshop脚本：单图层导出 - 最简单可靠的解决方案
// 每次只导出当前活动图层，避免所有多选检测问题

#target photoshop

// 主程序
try {
    if (app.documents.length == 0) {
        alert("请先打开一个文档！");
    } else {
        exportCurrentActiveLayer();
    }
} catch (e) {
    alert("脚本执行错误：" + e.message);
}

function exportCurrentActiveLayer() {
    var doc = app.activeDocument;
    
    try {
        var activeLayer = doc.activeLayer;
        var isGroup = (activeLayer.typename == "LayerSet");
        var typeText = isGroup ? "[图层组] " : "[图层] ";
        
        var confirmMessage = "准备导出当前活动图层：\n\n";
        confirmMessage += "• " + typeText + activeLayer.name + "\n\n";
        confirmMessage += "导出设置：\n";
        confirmMessage += "- 画布尺寸：" + doc.width + " x " + doc.height + "\n";
        confirmMessage += "- 保持位置：是\n";
        confirmMessage += "- 透明背景：是\n";
        confirmMessage += "- 格式：PNG\n\n";
        confirmMessage += "是否继续？";
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        // 选择导出位置
        var folder = Folder.selectDialog("请选择导出位置");
        if (!folder) return;
        
        // 保存当前状态
        var originalHistory = doc.activeHistoryState;
        
        try {
            exportSingleLayer(doc, activeLayer, folder);
            
            var successMessage = "导出成功！\n\n";
            successMessage += "图层：" + activeLayer.name + "\n";
            successMessage += "文件：" + cleanFileName(activeLayer.name) + ".png\n";
            successMessage += "位置：" + folder.fsName + "\n\n";
            successMessage += "提示：如需导出多个图层，请逐个选择图层并重复运行此脚本。";
            
            alert(successMessage);
            
        } catch (e) {
            alert("导出失败：" + e.message);
        } finally {
            // 恢复状态
            try {
                doc.activeHistoryState = originalHistory;
            } catch (e) {}
        }
        
    } catch (e) {
        alert("无法获取当前活动图层：" + e.message);
    }
}

function exportSingleLayer(doc, layer, folder) {
    var tempDoc = null;
    
    try {
        // 创建临时文档，尺寸与原画布完全相同
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp_export",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        var duplicatedLayer = layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var fileName = cleanFileName(layer.name) + ".png";
        var file = new File(folder.fsName + "/" + fileName);
        
        // 检查文件是否已存在
        if (file.exists) {
            if (!confirm("文件 '" + fileName + "' 已存在，是否覆盖？")) {
                throw new Error("用户取消覆盖文件");
            }
        }
        
        // 导出为PNG
        var options = new ExportOptionsSaveForWeb();
        options.format = SaveDocumentType.PNG;
        options.PNG8 = false; // 使用PNG-24，保证质量
        options.transparency = true; // 支持透明背景
        options.interlaced = false;
        options.includeProfile = false;
        options.optimized = true;
        
        tempDoc.exportDocument(file, ExportType.SAVEFORWEB, options);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        // 清理临时文档
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {}
        }
        
        // 确保切换回原文档
        try {
            app.activeDocument = doc;
        } catch (ex) {}
        
        throw e;
    }
}

function cleanFileName(name) {
    var result = name;
    
    // 逐个替换非法字符
    result = result.replace(/\\/g, "_");
    result = result.replace(/\//g, "_");
    result = result.replace(/:/g, "_");
    result = result.replace(/\*/g, "_");
    result = result.replace(/\?/g, "_");
    result = result.replace(/"/g, "_");
    result = result.replace(/</g, "_");
    result = result.replace(/>/g, "_");
    result = result.replace(/\|/g, "_");
    
    // 去除首尾空格
    while (result.charAt(0) == " ") {
        result = result.substring(1);
    }
    while (result.charAt(result.length - 1) == " ") {
        result = result.substring(0, result.length - 1);
    }
    
    // 限制长度
    if (result.length > 100) {
        result = result.substring(0, 100);
    }
    
    // 确保不为空
    if (result == "" || result == "_") {
        result = "layer_" + new Date().getTime();
    }
    
    return result;
}
