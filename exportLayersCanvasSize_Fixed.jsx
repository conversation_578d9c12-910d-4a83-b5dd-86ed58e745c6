// Photoshop脚本：按画布尺寸导出选中图层（修复版）
// 功能：
// 1. 导出当前选中的1个或多个图层
// 2. 以选中的图层命名导出
// 3. 导出的图层是以当前画布为大小
// 4. 导出对象在当前画布中的位置不变
// 5. 导入脚本后准备导出选择可以指定导出位置
// 6. 导出后弹出导出成功

#target photoshop

// 检查是否有打开的文档
if (app.documents.length == 0) {
    alert("请先打开一个文档！");
} else {
    main();
}

function main() {
    try {
        var doc = app.activeDocument;
        var selectedLayers = getSelectedLayers(doc);
        
        if (selectedLayers.length == 0) {
            alert("请先选择要导出的图层！\n\n提示：在图层面板中选择一个或多个图层");
            return;
        }
        
        // 显示选中的图层信息
        var layerNames = [];
        for (var i = 0; i < selectedLayers.length; i++) {
            layerNames.push("• " + selectedLayers[i].name);
        }
        
        var confirmMessage = "准备导出以下 " + selectedLayers.length + " 个图层：\n\n" + layerNames.join("\n") + "\n\n是否继续？";
        if (!confirm(confirmMessage)) {
            return;
        }
        
        // 选择导出文件夹
        var exportFolder = Folder.selectDialog("请选择导出位置");
        if (!exportFolder) {
            return;
        }
        
        // 保存当前状态
        var originalActiveLayer = doc.activeLayer;
        var originalHistory = doc.activeHistoryState;
        
        var successCount = 0;
        var failedLayers = [];
        
        // 批量导出
        for (var i = 0; i < selectedLayers.length; i++) {
            try {
                exportLayerAsCanvasSize(doc, selectedLayers[i], exportFolder);
                successCount++;
            } catch (e) {
                failedLayers.push("• " + selectedLayers[i].name + ": " + e.message);
            }
        }
        
        // 恢复原始状态
        try {
            doc.activeHistoryState = originalHistory;
            doc.activeLayer = originalActiveLayer;
        } catch (e) {
            // 忽略恢复错误
        }
        
        // 显示导出结果
        showExportResult(successCount, failedLayers, exportFolder);
        
    } catch (e) {
        alert("导出过程中发生错误：\n" + e.message);
    }
}

// 获取选中的图层
function getSelectedLayers(doc) {
    var selectedLayers = [];
    
    try {
        // 尝试获取多选图层
        var ref = new ActionReference();
        ref.putEnumerated(charIDToTypeID("Dcmn"), charIDToTypeID("Ordn"), charIDToTypeID("Trgt"));
        var desc = executeActionGet(ref);
        
        if (desc.hasKey(stringIDToTypeID("targetLayers"))) {
            var targetLayers = desc.getList(stringIDToTypeID("targetLayers"));
            for (var i = 0; i < targetLayers.count; i++) {
                var layerRef = targetLayers.getReference(i);
                var layerIndex = layerRef.getIndex();
                var layer = getLayerByIndex(doc, layerIndex);
                if (layer) {
                    selectedLayers.push(layer);
                }
            }
        } else {
            // 只有一个图层被选中
            selectedLayers.push(doc.activeLayer);
        }
    } catch (e) {
        // 如果获取失败，使用当前活动图层
        selectedLayers.push(doc.activeLayer);
    }
    
    return selectedLayers;
}

// 根据索引获取图层
function getLayerByIndex(doc, index) {
    try {
        var ref = new ActionReference();
        ref.putIndex(charIDToTypeID("Lyr "), index);
        var desc = executeActionGet(ref);
        var layerName = desc.getString(charIDToTypeID("Nm  "));
        
        // 在文档中查找对应名称的图层
        for (var i = 0; i < doc.layers.length; i++) {
            if (doc.layers[i].name == layerName) {
                return doc.layers[i];
            }
        }
    } catch (e) {
        // 忽略错误
    }
    return null;
}

// 导出单个图层为画布尺寸
function exportLayerAsCanvasSize(doc, layer, exportFolder) {
    var tempDoc = null;
    
    try {
        // 创建与原画布相同尺寸的临时文档
        tempDoc = app.documents.add(
            doc.width,
            doc.height,
            doc.resolution,
            "temp_export",
            NewDocumentMode.RGB,
            DocumentFill.TRANSPARENT
        );
        
        // 复制图层到临时文档
        app.activeDocument = doc;
        var duplicatedLayer = layer.duplicate(tempDoc, ElementPlacement.INSIDE);
        
        // 切换到临时文档
        app.activeDocument = tempDoc;
        
        // 生成文件名
        var cleanName = cleanFileName(layer.name);
        var fileName = cleanName + ".png";
        var exportFile = new File(exportFolder.fsName + "/" + fileName);
        
        // 导出为PNG
        exportAsPNG(tempDoc, exportFile);
        
        // 关闭临时文档
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        tempDoc = null;
        
        // 切换回原文档
        app.activeDocument = doc;
        
    } catch (e) {
        // 清理临时文档
        if (tempDoc) {
            try {
                tempDoc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (ex) {
                // 忽略关闭错误
            }
        }
        
        // 确保切换回原文档
        try {
            app.activeDocument = doc;
        } catch (ex) {
            // 忽略切换错误
        }
        
        throw new Error("导出图层失败: " + e.message);
    }
}

// 清理文件名
function cleanFileName(name) {
    var cleanName = name;
    
    // 替换非法字符
    cleanName = cleanName.replace(/\\/g, "_");
    cleanName = cleanName.replace(/\//g, "_");
    cleanName = cleanName.replace(/:/g, "_");
    cleanName = cleanName.replace(/\*/g, "_");
    cleanName = cleanName.replace(/\?/g, "_");
    cleanName = cleanName.replace(/"/g, "_");
    cleanName = cleanName.replace(/</g, "_");
    cleanName = cleanName.replace(/>/g, "_");
    cleanName = cleanName.replace(/\|/g, "_");
    
    // 移除首尾空格
    cleanName = cleanName.replace(/^\s+|\s+$/g, "");
    
    // 限制长度
    if (cleanName.length > 100) {
        cleanName = cleanName.substring(0, 100);
    }
    
    // 确保不为空
    if (cleanName == "" || cleanName == "_") {
        cleanName = "layer_" + new Date().getTime();
    }
    
    return cleanName;
}

// 导出为PNG
function exportAsPNG(doc, file) {
    var options = new ExportOptionsSaveForWeb();
    options.format = SaveDocumentType.PNG;
    options.PNG8 = false; // 使用PNG-24
    options.transparency = true;
    options.interlaced = false;
    options.includeProfile = false;
    options.optimized = true;
    
    doc.exportDocument(file, ExportType.SAVEFORWEB, options);
}

// 显示导出结果
function showExportResult(successCount, failedLayers, exportFolder) {
    var resultMessage = "=== 导出完成 ===\n\n";
    resultMessage += "✓ 成功导出：" + successCount + " 个图层\n";
    resultMessage += "📁 导出位置：" + exportFolder.fsName + "\n";
    
    if (failedLayers.length > 0) {
        resultMessage += "\n❌ 失败：" + failedLayers.length + " 个图层\n";
        resultMessage += failedLayers.join("\n");
    }
    
    if (successCount > 0) {
        resultMessage += "\n🎉 导出成功！";
    }
    
    alert(resultMessage);
}
