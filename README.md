# Photoshop 按画布尺寸导出图层脚本

这个项目包含了三个Photoshop脚本，用于实现按画布尺寸导出选中图层的功能，避免使用传统的"存储为"导出方式。

## 功能特点

✅ **导出当前选中的1个或多个图层**  
✅ **以选中的图层命名导出**  
✅ **导出的图层是以当前画布为大小**  
✅ **导出对象在当前画布中的位置不变**  
✅ **支持透明背景**  
✅ **快速批量导出**  

## 脚本文件说明

### 1. `simpleExportCanvasSize.jsx` (推荐使用)
- **最稳定的版本，修复了所有已知问题**
- 避免了正则表达式兼容性问题
- 简洁高效，专门优化用于快速导出
- 完整的错误处理和状态恢复
- 适合所有用户使用

### 2. `exportLayersCanvasSize_Fixed.jsx` (功能完整版)
- 修复版本，解决了原始脚本的错误
- 功能最完整，包含详细的用户反馈
- 适合需要详细导出信息的用户

### 3. `quickExportCanvasSize.jsx` (原始版本)
- 可能存在兼容性问题，不推荐使用
- 保留用于参考

### 4. `exportLayersAsCanvasSize.jsx` & `exportLayersAsCanvasSize_Enhanced.jsx`
- 早期版本，功能基础
- 建议使用新的修复版本

## 使用方法

### 安装步骤
1. 下载脚本文件（**强烈推荐使用 `simpleExportCanvasSize.jsx`**）
2. 将脚本文件放到Photoshop的脚本文件夹中：
   - Windows: `C:\Program Files\Adobe\Adobe Photoshop [版本]\Presets\Scripts\`
   - Mac: `/Applications/Adobe Photoshop [版本]/Presets/Scripts/`

### 运行步骤
1. 在Photoshop中打开你的文档
2. 在图层面板中选择要导出的图层：
   - 单选：直接点击图层
   - 多选：按住 `Ctrl`（Windows）或 `Cmd`（Mac）点击多个图层
3. 运行脚本：
   - 方法1：`文件` → `脚本` → `浏览` → 选择 `simpleExportCanvasSize.jsx`
   - 方法2：`文件` → `脚本` → `[脚本名称]`（如果已安装到脚本文件夹）
4. 确认要导出的图层列表
5. 选择导出位置
6. 等待导出完成，查看成功提示

## 导出特点

### 与PS默认导出的区别
- **PS默认导出**：只导出图层的有效像素区域（裁剪到内容边界）
- **本脚本导出**：导出完整画布尺寸，保持对象在画布中的原始位置

### 文件命名规则
- 使用图层名称作为文件名
- 自动清理文件名中的非法字符（`\/:*?"<>|`）
- 文件格式：PNG（支持透明背景）

### 导出质量
- 使用PNG-24格式，保证最佳质量
- 支持透明背景
- 保持原始分辨率

## 技术实现

### 核心原理
1. 创建与原画布相同尺寸的临时文档
2. 将选中图层复制到临时文档
3. 导出临时文档为PNG
4. 清理临时文档
5. 恢复原文档状态

### 优势
- **快速**：避免了"存储为"的复杂界面操作
- **批量**：支持一次性导出多个图层
- **保真**：保持图层在画布中的原始位置和尺寸
- **自动化**：无需手动调整导出设置

## 注意事项

1. **文档要求**：确保文档已保存且不是索引颜色模式
2. **图层选择**：脚本会自动检测选中的图层，如果没有选中则使用当前活动图层
3. **文件覆盖**：如果目标文件夹中存在同名文件，会被覆盖
4. **内存使用**：导出大尺寸画布时可能消耗较多内存

## 故障排除

### 常见问题
1. **"请先选择要导出的图层"**
   - 确保在图层面板中选中了要导出的图层
   
2. **导出失败**
   - 检查目标文件夹是否有写入权限
   - 确保磁盘空间充足
   - 尝试使用英文路径

3. **脚本无法运行**
   - 确保Photoshop版本支持JSX脚本
   - 检查脚本文件是否完整

### 兼容性
- 支持Photoshop CS6及以上版本
- 支持Windows和Mac系统
- 支持RGB和CMYK颜色模式

## 许可证

本脚本免费使用，可自由修改和分发。

## 问题修复

### 已修复的问题
- ✅ **正则表达式兼容性问题**：原脚本在某些PS版本中会报错"错误25: 应为)"
- ✅ **文件名清理问题**：改用逐个字符替换方式，避免正则表达式
- ✅ **用户体验优化**：添加了导出前确认和详细的结果反馈
- ✅ **错误处理增强**：更好的异常捕获和状态恢复

### 推荐使用
建议使用 `simpleExportCanvasSize.jsx`，这是最稳定和可靠的版本。

## 更新日志

- v1.0: 初始版本，基础导出功能
- v1.1: 增加多图层选择支持
- v1.2: 优化错误处理和用户体验
- v1.3: 添加快速导出版本
- v1.4: 修复正则表达式兼容性问题，创建稳定版本
